const { logger } = require("../utils");
var { PutObjectCommand, S3Client } = require("@aws-sdk/client-s3");
var { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const axios = require("axios");
const mime = require("mime-types");
const fs = require("fs");
const path = require("path");

module.exports = async function ({ Services, config }) {
	return {
		getSignedUrlFromS3: async ({ fileName, folderName = "Quotes" }) => {
			try {
				const client = new S3Client({
					credentials: {
						accessKeyId: config.aws.accessKeyId,
						secretAccessKey: config.aws.secretAccessKey,
					},
					region: config.aws.region,
				});

				// Ensure the file is uploaded inside the "apbungalow/" folder
				const fileKey = `apbungalow/${folderName}/${fileName}`;

				// Determine the content type based on the file extension
				const contentType = mime.lookup(fileName) || "application/octet-stream";

				const command = new PutObjectCommand({
					Bucket: config.aws.bucket,
					Key: fileKey,
					ContentType: contentType,
					ACL: "public-read", // Make the uploaded file publicly accessible
				});

				// Generate a signed URL for uploading the file (expires in 3600 seconds)
				const signedUrl = await getSignedUrl(client, command, { expiresIn: 3600 });

				// Construct the public URL based on S3 bucket URL format
				const publicUrl = `https://${config.aws.bucket}.s3.${config.aws.region}.amazonaws.com/${fileKey}`;

				return {
					ok: true,
					signedUrl,
					publicUrl, // The file will be accessible at this URL once uploaded
				};
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					error: error.message,
				};
			}
		},
		uploadFileToS3: async ({ signedUrl, file }) => {
			try {
				// Resolve the absolute file path
				const absolutePath = path.resolve(file);
				console.log(`Uploading file: ${absolutePath}`);

				// Get file size
				const stats = fs.statSync(absolutePath);
				const fileSize = stats.size;
				//   console.log(`File size: ${fileSize} bytes`);

				// Prepare stream and content type
				const fileStream = fs.createReadStream(absolutePath);
				const contentType = mime.lookup(absolutePath) || "application/octet-stream";

				// Perform the upload with required headers
				const response = await axios.put(signedUrl, fileStream, {
					headers: {
						"Content-Type": contentType,
						"Content-Length": fileSize.toString(), // Set file size to avoid chunked encoding
					},
				});

				//   console.log(`Upload response status: ${response.status}`);
				//   console.log(`Upload response headers: ${JSON.stringify(response.headers)}`);

				if (response.status === 200) {
					return {
						ok: true,
						message: "File uploaded successfully",
					};
				} else {
					return {
						ok: false,
						error: "Failed to upload file",
					};
				}
			} catch (error) {
				console.error("Upload error:", error);
				return {
					ok: false,
					error: error.message,
				};
			}
		},
	};
};
