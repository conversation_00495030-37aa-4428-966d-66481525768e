const { logger } = require("../utils");
const config = require("../config/conf");
const axios = require("axios");
module.exports.ADLogin = async ({ email, password }) => {
    try {
        const response = await axios({
            url: `${config.ap.baseUrl}/loginad`,
            method: "POST",
            auth: {
                username: email,
                password: password
            },
            headers: {
                "Content-Type": "application/json"
            }
        });
        return { ok: true, data: response.data}
    } catch (error) {
        logger.error(error)
        return { ok: false, message: "AD Login Failed" };
    }
}