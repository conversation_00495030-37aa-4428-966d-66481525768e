const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');

async function createExcelFile({data, fileName, additionalSheets = []}) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet 1');

    // Add column headers
    worksheet.columns = Object.keys(data[0]).map(key => ({ header: key, key: key }));

    // Add rows
    data.forEach(item => {
        worksheet.addRow(item);
    });

    // Add additional sheets if provided
    additionalSheets.forEach(sheet => {
        const additionalWorksheet = workbook.addWorksheet(sheet.name);
        
        if (sheet.data && sheet.data.length > 0) {
            // Add column headers for the additional sheet
            additionalWorksheet.columns = Object.keys(sheet.data[0]).map(key => ({ header: key, key: key }));
            
            // Add rows to the additional sheet
            sheet.data.forEach(item => {
                additionalWorksheet.addRow(item);
            });
        }
    });

    // Ensure tmp directory exists
    const tmpDir = path.join(__dirname, '../tmp');
    if (!fs.existsSync(tmpDir)) {
        fs.mkdirSync(tmpDir);
    }

    // Save the file
    const filePath = path.join(tmpDir, fileName);
    await workbook.xlsx.writeFile(filePath);

    // console.log(filePath)
    return filePath;
}

module.exports = {
    createExcelFile
};