const { logger } = require("../utils");
const axios = require("axios");
const exceljs = require("exceljs");
const fs = require("fs");
const sharp = require("sharp");
const { formatDateToIST } = require("./../helpers/mobileEmailValidator");

module.exports = async function ({ config, Services }) {
	return {
		/**
		 * Creates a new wishlist for a customer
		 */
		generateSfToken: async function () {
			logger.info("Generating SF token");

			const payload = {
				method: "POST",
				url: process.env.SF_AUTH_BASE_URL,
				headers: {
					"Content-Type": "application/x-www-form-urlencoded",
				},
				data: {
					grant_type: "password",
					client_id: process.env.SF_CLIENT_ID,
					client_secret: process.env.SF_CLIENT_SECRET,
					username: process.env.SF_AUTH_USER,
					password: process.env.SF_AUTH_PASSWORD,
				},
			};

			const response = await axios(payload);
			// set the token in the config
			config.sf.token = response.data.access_token;
			// logger.debug(response.data.access_token)
		},
		generateSaleOrderZohoToken: async function () {
			logger.info("Generating ZOHO token");
			const payload = {
				method: "POST",
				url: `${process.env.ZOHO_ACCOUNTS_BASE_URL}/oauth/v2/token?client_id=${process.env.ZOHO_CLIENT_ID}&client_secret=${process.env.ZOHO_CLIENT_SECRET}&grant_type=refresh_token&refresh_token=${process.env.ZOHO_REFRESH_TOKEN}`,
			};

			const response = await axios(payload);
			// set the token in the config
			config.zoho.sales_order_token = response.data.access_token;
		},

		/**
		 * Syncs product SKUs and item_ids from Zoho to our database.
		 * This function is intended to be run as a cron job.
		 */
		syncZohoSkuAndItemIds: async function () {
			logger.debug("Syncing Zoho SKU and Item IDs...");

			// Establish database connection
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				// Initialize ZohoHelper service
				const Services = { ZohoHelper: await require("../services/ZohoHelper")({ Services: {}, config }) };

				const BATCH_SIZE = 500;
				let totalUpdated = 0;

				let page = 0; // Start from page 0
				let hasMoreProducts = true;

				// Fetch all product data from Zoho in one go
				const allZohoItems = [];
				let zohoPage = 1;
				let hasMorePages = true;

				while (hasMorePages) {
					const zohoResponse = await Services.ZohoHelper.getProductStockAndPrice({ page: zohoPage });
					allZohoItems.push(...zohoResponse.items);
					hasMorePages = zohoResponse.page_context.has_more_page;
					zohoPage++;
				}

				logger.debug(`Fetched ${allZohoItems.length} items from Zoho`);

				// Create a Map for fast lookup of item_id by SKU
				const zohoItemMap = new Map(allZohoItems.map((item) => [item.sku, item.item_id]));

				while (hasMoreProducts) {
					// Fetch a batch of products that need syncing with pagination
					const products = await db
						.collection("products")
						.find({ item_id: { $exists: false } }, { projection: { code: 1 } })
						.skip(page * BATCH_SIZE) // ✅ Skip processed items
						.limit(BATCH_SIZE)
						.toArray();

					if (products.length === 0) {
						logger.info("No more products to process. Exiting loop.");
						break; // Exit loop if no products are found
					}

					logger.debug(`Fetched ${products.length} products from database (Page: ${page})`);

					const bulkOps = [];
					for (const product of products) {
						const item_id = zohoItemMap.get(product.code);
						if (item_id) {
							bulkOps.push({
								updateOne: {
									filter: { code: product.code },
									update: { $set: { item_id } },
								},
							});
						}
					}

					if (bulkOps.length > 0) {
						const result = await db.collection("products").bulkWrite(bulkOps);
						totalUpdated += result.modifiedCount;
						logger.info(`Synced SKU/item_id for ${result.modifiedCount} products in this batch`);
					} else {
						logger.info("No SKU/item_id updates needed in this batch.");
					}

					page++; // ✅ Move to the next batch
				}

				logger.info(`Total SKU/item_id updates completed: ${totalUpdated}`);
			} catch (error) {
				logger.error(error.message);
			}
		},

		/**
		 * Removes unapproved hold requests and approved hold requests after 48hours.
		 * This function is intended to be run as a cron job.
		 */
		removeUnApprovedHoldRequests: async function () {
			// Establish database connection
			const db = await Services.MongoHelper.getDatabaseConnection();

			try {
				// Initialize SalesforceHelper service
				const Services = {
					SalesforceHelper: await require("../services/SalesforceHelper")({ Services: {}, config }),
				};
				logger.info("Removing unapproved hold requests");

				// Get hold requests created before 48 hours and not deleted
				const holdRequests = await db
					.collection("holdRequests")
					.find({
						createdAt: { $lt: new Date(Date.now() - 48 * 60 * 60 * 1000) },
						isDeleted: false,
					})
					.toArray();

				// Prepare bulk operations
				const holdRequestOps = [];
				const productOps = [];

				// Process each hold request
				const updatePromises = holdRequests.map(async (holdReq) => {
					const filter = { holdCRMID: holdReq.holdCRMID };

					if (holdReq.holdStatus === "Hold Approval pending") {
						// Nullify pending hold requests in Salesforce after 48 hours
						await Services.SalesforceHelper.nullifyHoldRequest({
							holdCRMID: holdReq.holdCRMID,
							salesRepPID: holdReq.salesRepPid,
							status: "hold nullified",
						});

						// Update status and mark as deleted in DB
						holdRequestOps.push({
							updateOne: {
								filter,
								update: {
									$set: { holdStatus: "Hold nullified", updatedAt: new Date(), isDeleted: true },
								},
							},
						});
					}

					// Expire approved hold requests after 48 hours
					if (holdReq.holdStatus === "Hold Approved") {
						holdRequestOps.push({
							updateOne: {
								filter,
								update: {
									$set: { holdStatus: "Hold period expired", updatedAt: new Date(), isDeleted: true },
								},
							},
						});

						// Calculate total quantity to reduce from hold
						const sumOfQuantity = Object.values(holdReq.warehouseWiseQuantity).reduce(
							(sum, item) => sum + item,
							0
						);

						productOps.push({
							updateOne: {
								filter: { code: holdReq.skuCode },
								update: {
									$inc: {
										unitsOnHold: -sumOfQuantity,
										...Object.keys(holdReq.warehouseWiseQuantity).reduce((obj, warehouse) => {
											obj[`warehouseWiseQuantity.${warehouse}`] =
												-holdReq.warehouseWiseQuantity[warehouse];
											return obj;
										}, {}),
									},
								},
							},
						});
					}
				});

				// Wait for all hold requests to be processed
				await Promise.all(updatePromises);

				// Execute bulk updates for holdRequests
				if (holdRequestOps.length > 0) {
					const result = await db.collection("holdRequests").bulkWrite(holdRequestOps);
					logger.info(`Updated ${result.modifiedCount} hold requests`);
				}

				// Execute bulk updates for products
				if (productOps.length > 0) {
					const result = await db.collection("products").bulkWrite(productOps);
					logger.info(`Updated ${result.modifiedCount} products`);
				}

				logger.debug(`Removed unapproved hold requests.`);
			} catch (error) {
				logger.error(error.message);
			}
		},

		/**
		 * Makes all active sessions inactive for all customers
		 * This function is typically used to log out all customers, for example during maintenance or security updates
		 */
		makeSessionInactiveForAllCustomers: async function () {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();

			try {
				// Update all active sessions to inactive
				const result = await db
					.collection("sessions")
					.updateMany(
						{ isActive: true },
						{ $set: { isActive: false, updatedAt: Date.now(), sessionEndTime: Date.now() } }
					);

				// Log the number of updated sessions
				logger.info(`Updated ${result.modifiedCount} sessions to inactive`);
			} catch (error) {
				logger.error(error.message);
			}
		},

		/**
		 * Makes all active sessions inactive for all sales persons and logs logout events
		 * This function forcefully logs out all sales persons and tracks the events in the login logs
		 */
		logoutAllSalesPersons: async function () {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();

			try {
				// Get all tokens from oidcs collection
				const oidcsEntries = await db.collection("oidcs").find({}).toArray();
				logger.info(`Found ${oidcsEntries.length} logged  in users`);

				// Log logout events for each sales person and delete from oidcs
				const logPromises = oidcsEntries.map(async (oidcs) => {
					// Get user details from the salesPersonId in the oidcs record
					const user = await db.collection("salesPersons").findOne({ _id: oidcs.salesPersonId });

					if (user && user.employeecode) {
						// Log the logout event
						await Services.Tracker.logEvent({
							employeecode: user.employeecode,
							event: "logout",
							timestamp: new Date(),
						});
					}
				});

				// Wait for all log events to complete
				await Promise.all(logPromises);

				// Delete all entries from oidcs collection
				const deleteResult = await db.collection("oidcs").deleteMany({});

				// Log the number of updated sessions and deleted oidcs entries
				logger.info(`Logged out ${deleteResult.deletedCount} sales persons with events recorded`);
			} catch (error) {
				logger.error(`Error logging out sales persons: ${error.message}`);
			}
		},

		/**
		 *  Creates token for helo.ai whatsapp
		 *  This function is used for creating token for whatsapp messaging.
		 */
		generateWhatsappToken: async function () {
			try {
				logger.info("Generating whatsapp token");
				const response = await axios({
					method: "POST",
					url: "https://wabaapp.helo.ai/user/signIn",
					headers: {
						"Content-Type": "application/json",
					},
					data: {
						userName: config.helo.userName,
						password: config.helo.password,
					},
				});
				config.helo.token = response.data.data.token;
				logger.info("Generated whatsapp token Successfully");
			} catch (error) {
				console.log("error", error);
				throw error;
			}
		},

		/**
		 * Fetches CSV files from PIM server and downloads them to the project's tmp folder.
		 * This function connects to the PIM server via SFTP, lists files in a specific directory,
		 * and downloads all CSV files found.
		 */
		fetchFileFromPIMAndUpdateDB: async function () {
			logger.info("Fetching file from PIM and updating DB");
			const Client = require("ssh2-sftp-client");
			const path = require("path");

			// SFTP connection parameters
			const params = {
				host: config.pim.host,
				port: config.pim.port,
				username: config.pim.username,
				password: config.pim.password,
			};

			const sftp = new Client();
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				// Connect to the SFTP server
				await sftp.connect(params);

				// List files in the specified directory
				const data = await sftp.list("/var/ftp/Adobe_Analytics/PIM/Bungalow");

				// Filter for CSV files and create download promises
				const downloadPromises = data
					.filter((file) => file.type === "-" && (file.name.endsWith(".csv") || file.name.endsWith(".xlsx")))
					.map(async (file) => {
						const localPath = path.join(__dirname, "..", "tmp", file.name);
						try {
							// Download each CSV file
							await sftp.get(`/var/ftp/Adobe_Analytics/PIM/Bungalow/${file.name}`, localPath);
							console.log(`Downloaded ${file.name} to project's tmp folder`);
						} catch (err) {
							console.error(`Error downloading ${file.name}: ${err.message}`);
						}
					});

				// Wait for all downloads to complete
				await Promise.all(downloadPromises);

				// prepare path to read
				const tmpDir = path.join(__dirname, "..", "tmp");
				const files = fs.readdirSync(tmpDir);
				const fileToRead = files.find((file) => file.startsWith(`Bungalow_Products`));
				const pathToRead = path.join(__dirname, "..", "tmp", fileToRead);

				// Create a new ExcelJS workbook
				const workbook = new exceljs.Workbook();

				// Read the CSV file
				await workbook.csv.readFile(pathToRead);
				const worksheet = workbook.getWorksheet(1);
				const sheetData = [];

				// Get the headers
				let sheetHeaders = worksheet.getRow(1).values;
				sheetHeaders.shift(); // Remove the first element which is empty
				sheetHeaders = sheetHeaders.map((header) => header.trim()); // Remove leading and trailing spaces
				worksheet.eachRow((row, rowNumber) => {
					if (rowNumber > 1) {
						// Assuming the first row is headers
						const rowData = { images: [], productImages: [] }; // Initialize images array
						row.eachCell((cell, colNumber) => {
							const header = sheetHeaders[colNumber - 1];
							if (header.startsWith("Bung_")) {
								// Push non-empty values to images array
								if (cell.value != null) {
									rowData.images.push(cell.value);
									rowData.productImages.push({
										image: cell.value,
										updatedAt: new Date(),
										uploadedFrom: "PIM",
									});
								}
							} else {
								switch (header) {
									case "SKU code":
										rowData.code = cell.value?.toString().trim(); // Trim whitespace from SKU code
										break;
									case "Category":
										rowData.category = cell.value?.toString().toUpperCase();
										break;
									case "Sub Category":
										rowData.subCategory = cell.value?.toString().toUpperCase();
										break;
									case "Product description":
										rowData.description = cell.value;
										break;
									case "Brand name":
										rowData.Brand = cell.value;
										break;
									case "MOQ":
										rowData.MOQ = cell.value || 1; // Default to 1 if empty
										break;
									case "FinishName(Material 1)":
										rowData.material = cell.value;
										break;
									case "Material3(Exclusivity)":
										rowData.color = cell.value;
										break;
									case "Fabric name(Material 2)":
										rowData.techinque = cell.value;
										break;
									case "Anthology_Space":
										rowData.space = cell.value?.toString().toUpperCase();
										break;
									case "Remarks":
										rowData.packaging = cell.value;
										break;
									case "OrderType":
										rowData.productType = cell.value || "N/A";
										break;
									default:
										rowData[header] = cell.value; // Other headers as-is
								}
							}
						});
						sheetData.push(rowData);
					}
				});

				// Get all SKU codes from the Excel file
				const validSKUs = new Set(sheetData.map((item) => item.code.trim()));

				// Now update the DB with the sheet data 100 at a time
				const batchSize = 100;
				let totalUpdated = 0;
				let totalUpserted = 0;

				logger.info(`Processing ${sheetData.length} products from PIM in batches of ${batchSize}`);

				for (let i = 0; i < sheetData.length; i += batchSize) {
					const batch = sheetData.slice(i, i + batchSize);

					// Process each product in the batch
					const bulkOps = [];

					for (const row of batch) {
						// Fetch the existing product to merge images
						const existingProduct = await db.collection("products").findOne({ code: row.code.trim() });

						let mergedProductImages = [];

						if (existingProduct && existingProduct.productImages) {
							// Get existing image URLs for comparison
							const existingImageUrls = existingProduct.productImages.map((img) => img.image);

							// Filter out PIM images that already exist
							const newPimImages = row.productImages.filter(
								(img) => !existingImageUrls.includes(img.image)
							);

							// Keep all existing images in their original order and append new images
							mergedProductImages = [
								...existingProduct.productImages, // Maintain original order of ALL existing images
								...newPimImages, // Append only new images
							];
						} else {
							mergedProductImages = row.productImages;
						}

						// Create the update operation
						bulkOps.push({
							updateOne: {
								filter: { code: row.code.trim() },
								update: {
									$set: {
										...row,
										productImages: mergedProductImages,
										updatedAt: new Date(),
										isDeleted: false,
									},
									$setOnInsert: {
										createdAt: new Date(),
										createdBy: "PIM",
									},
								},
								upsert: true,
							},
						});
					}

					// Execute the bulk operations
					const result = await db.collection("products").bulkWrite(bulkOps);
					
					// Track the results
					totalUpdated += result.modifiedCount;
					totalUpserted += result.upsertedCount;
					
					logger.info(`Batch ${Math.floor(i / batchSize) + 1}: Processed ${batch.length} products - Updated: ${result.modifiedCount}, Inserted: ${result.upsertedCount}`);
				}

				// Mark missing products as `isDeleted: true`
				const deleteResult = await db.collection("products").updateMany(
					{ code: { $nin: Array.from(validSKUs) } }, // Products NOT in the new sheet
					{ $set: { isDeleted: true, updatedAt: new Date() } }
				);

				logger.info(`PIM sync completed - Total Updated: ${totalUpdated}, Total Inserted: ${totalUpserted}, Products marked as deleted: ${deleteResult.modifiedCount}`);
			} catch (err) {
				logger.error(`PIM operation failed: ${err.message}`);
			} finally {
				// Ensure SFTP connection is closed
				await sftp.end();
			}
		},

		/**
		 *  Function to retrieve tax and pricing information from Zoho
		 *  This function is used to retrieve tax and pricing information from Zoho
		 */

		getTaxAndPricingFromZoho: async function () {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				logger.info(`started getTaxAndPricingFromZoho  cron`);

				// Fetch all products from the database
				const products = await db
					.collection("products")
					.find({ isDeleted: false, item_id: { $exists: true } })
					.sort({ _id: 1})
					.toArray();

				// Set batch size for processing
				const batchSize = 200;

				// Process products in batches
				for (let i = 0; i < products.length; i += batchSize) {
					// Slice the current batch of products
					const batch = products.slice(i, i + batchSize);

					// Create a comma-separated string of item_ids
					const item_ids = batch
						.filter((product) => product.item_id)
						.map((product) => product.item_id)
						.join(",");
					// logger.debug(JSON.stringify(item_ids));
					// Fetch item details from Zoho
					const response = await axios({
						method: "GET",
						url: `https://www.zohoapis.in/books/v3/itemdetails?item_ids=${item_ids}&organization_id=${config.zoho.organization_id}`,
						headers: {
							Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
							"Content-Type": "application/json",
						},
					});

					// Prepare bulk update operations
					const bulkOps = response.data.items.map((item) => ({
						updateOne: {
							// Use compound filter to check both item_id and code to prevent duplicates
							filter: {
								$or: [
									{ item_id: item.item_id },  // Try to find by item_id first
									{ code: item.sku }          // Fall back to finding by SKU code
								]
							},
							update: {
								$set: {
									code: item.sku,
									item_id: item.item_id,
									stock: item.available_for_sale_stock || 0,
									price: +item.purchase_rate,
									mrp: +item.label_rate,
									discount: +item?.cf_structured_discount || 0, // In the current API the discount is not coming.
									hsn_or_sac: item?.hsn_or_sac || "",
									unit: item.unit,
									status: item.status,
									tax_id: item.tax_id || "",
									tax_name: item.tax_name || "",
									item_type: item.item_type,
									tax_exemption_code: item.tax_exemption_code,
									warehouse_name: item?.warehouses[0]?.warehouse_name || "",
									project_id: item?.project_id || "",
									template_id: item?.associated_template_id || "",
									documents: item?.documents || [],
									warehouses:
										item?.warehouses
											?.filter((wareH) =>
												["1001-16A8", "1001-Display", "1001-9504"].includes(
													wareH.warehouse_name
												)
											)
											.map((wareH) => ({
												warehouse_id: wareH.warehouse_id,
												warehouse_name: wareH.warehouse_name,
												warehouse_stock: wareH.warehouse_available_for_sale_stock,
											})) || [],
									item_tax_preferences: item.item_tax_preferences,
									// Ensure both Zoho and PIM data coexist
									createdByZoho: true,
									updatedAt: new Date(),
								},
							},
						},
					}));

					// Execute bulk write operation
					const updateRes = await db.collection("products").bulkWrite(bulkOps);

					// Log update results
					logger.info(`Updated Count: ${updateRes.modifiedCount}`);
					logger.info(`Updated tax and pricing information for item_ids: ${item_ids}`);
				}

				logger.info("Completed updating tax and pricing information for all products");
			} catch (error) {
				// Log and rethrow any errors
				logger.error("Error getting tax and pricing from Zoho:", error);
			}
		},

		convertToBase64Bulk: async ({ skus = [] } = {}) => {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				logger.info("Started convertToBase64Bulk cron");

				// if skus are provided, fetch only those products
				let products = [];
				if (skus && skus?.length > 0) {
					products = await db
						.collection("products")
						.find({ isDeleted: false, code: { $in: skus } })
						.toArray();
				} else {
					// Fetch all products that are not deleted and missing the base64Image field
					products = await db
						.collection("products")
						.find({
							isDeleted: false,
							base64Image: { $exists: false },
							productImages: { $exists: true, $ne: [] },
						})
						.toArray();
				}

				logger.info(`Found ${products.length} products to update.`);

				// Process products in batches of BATCH_SIZE
				const BATCH_SIZE = 200;
				for (let i = 0; i < products.length; i += BATCH_SIZE) {
					const batch = products.slice(i, i + BATCH_SIZE);
					const updateOperations = [];

					for (const product of batch) {
						// Ensure the product has a productImages array and it's not empty
						if (!product.productImages || product.productImages.length === 0) {
							logger.warn(`Product ${product._id} has no productImages. Skipping.`);
							continue;
						}

						try {
							// Sort productImages by updatedAt in descending order to get the latest image
							// const sortedImages = product.productImages.sort(
							// 	(a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
							// );

							// // Get the most recently updated image
							// const latestImage = sortedImages[0];
							const latestImage = product.productImages[0];

							// Fetch the image from the latest image URL
							const response = await fetch(latestImage.image);
							if (!response.ok) {
								throw new Error(
									`Failed to fetch image for product ${product._id}: ${response.statusText}`
								);
							}

							// Convert response to Buffer using arrayBuffer
							const arrayBuffer = await response.arrayBuffer();
							const imageBuffer = Buffer.from(arrayBuffer);

							// Resize the image while maintaining aspect ratio
							// Set max width to 500px, height will be calculated automatically to maintain ratio
							const resizedBuffer = await sharp(imageBuffer)
								.resize(500, null, { 
									fit: 'inside',
									withoutEnlargement: true 
								})
								.toBuffer();

							// Convert the resized image to a Base64 string
							const base64Image = resizedBuffer.toString("base64");

							// Queue the update operation for this product
							updateOperations.push({
								updateOne: {
									filter: { _id: product._id },
									update: { $set: { base64Image } },
								},
							});
						} catch (error) {
							logger.error(`Error processing product ${product._id}:`, error);
						}
					}

					// Perform bulk update for the current batch
					if (updateOperations.length > 0) {
						try {
							const bulkResult = await db.collection("products").bulkWrite(updateOperations);
							logger.info(
								`Batch update completed for products ${i} to ${i + BATCH_SIZE - 1}. Matched: ${
									bulkResult.matchedCount
								}, Modified: ${bulkResult.modifiedCount}`
							);
						} catch (error) {
							logger.error(`Bulk update error for batch starting at index ${i}:`, error);
						}
					} else {
						logger.info(`No update operations in batch starting at index ${i}`);
					}
				}

				logger.info("Bulk update processing completed.");
			} catch (error) {
				logger.error("Error processing bulk update:", error);
			}
		},

		/**
		 * Retrieves tax and pricing information from Zoho and updates the database.
		 *
		 * This function fetches the latest product details from Zoho, including stock, pricing,
		 * and tax information. It processes the data in batches, updates the MongoDB database,
		 * and ensures only recently modified products are fetched using `last_modified_time`.
		 *
		 * @async
		 * @function lastModifiedProducts
		 * @throws {Error} Logs and throws an error if the operation fails.
		 */
		lastModifiedProducts: async function () {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				logger.info("Started lastModifiedProducts cron");

				// Fetch the last modified time from salesOrders collection
				const lastModifiedRecord = await db.collection("salesOrders").findOne({ name: "last_modified_time" });
				let lastModifiedTime =
					lastModifiedRecord?.last_modified_time || new Date(Date.now() - 24 * 60 * 60 * 1000);

				// Convert to "YYYY-MM-DDTHH:mm:ss+0530"
				lastModifiedTime = formatDateToIST(new Date(lastModifiedTime));
				// lastModifiedTime = formatDateToIST(new Date("2025-02-28T16:00:03+0530"));
				logger.info(`Using last_modified_time: ${lastModifiedTime}`);

				// Prepare common Zoho API request headers
				const zohoHeaders = {
					Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
					"Content-Type": "application/json",
				};

				// Fetch all Zoho items using pagination
				let page = 1;
				let hasMorePages = true;
				let allZohoItems = [];

				// fetch the products from zoho by passing warehouse_id one by one since we have 3 warehouses ["1001-16A8", "1001-Display", "1001-9504"]
				while (hasMorePages) {
					const url = `https://www.zohoapis.in/books/v3/items?organization_id=${
						config.zoho.organization_id
					}&page=${page}&per_page=200&filter_by=Status.Active&sort_column=last_modified_time&sort_order=D&usestate=true&last_modified_time=${encodeURIComponent(
						lastModifiedTime
					)}`;

					const { data } = await axios.get(url, { headers: zohoHeaders });
					if (data?.items) {
						allZohoItems.push(...data.items);
					}
					hasMorePages = data?.page_context?.has_more_page || false;
					page++;
				}

				logger.info(`Fetched ${allZohoItems.length} items from Zoho`);

				if (allZohoItems.length === 0) {
					logger.info("No new products to update.");
					return;
				}

				// Process items in batches for updating
				const batchSize = 200;
				for (let i = 0; i < allZohoItems.length; i += batchSize) {
					const batch = allZohoItems.slice(i, i + batchSize);

					// Build a comma-separated list of item_ids
					const itemIds = batch
						.filter((product) => product.item_id)
						.map((product) => product.item_id)
						.join(",");

					// Fetch item details from Zoho for the current batch
					const detailsUrl = `https://www.zohoapis.in/books/v3/itemdetails?item_ids=${itemIds}&organization_id=${config.zoho.organization_id}`;
					const { data: detailsData } = await axios.get(detailsUrl, {
						headers: zohoHeaders,
					});

					// Prepare bulk update operations based on Zoho item details
					const bulkOps = detailsData.items.map((item) => ({
						updateOne: {
							// Use compound filter to check both item_id and code to prevent duplicates
							filter: {
								$or: [
									{ item_id: item.item_id },  // Try to find by item_id first
									{ code: item.sku }          // Fall back to finding by SKU code
								]
							},
							update: {
								$set: {
									code: item.sku,
									item_id: item.item_id,
									stock: item.available_for_sale_stock || 0, // use available stock from new api
									price: +item.purchase_rate,
									mrp: +item.label_rate,
									discount: +item?.cf_structured_discount || 0, // item.custom_fields.find(field=> field.lael === "cf_structured_discount").value
									hsn_or_sac: item?.hsn_or_sac || "",
									unit: item.unit,
									status: item.status,
									tax_id: item.tax_id || "",
									tax_name: item.tax_name || "",
									item_type: item.item_type,
									tax_exemption_code: item.tax_exemption_code,
									warehouse_name: item?.warehouses?.[0]?.warehouse_name || "",
									project_id: item?.project_id || "",
									template_id: item?.associated_template_id || "",
									documents: item?.documents || [],
									warehouses:
										item?.warehouses
											?.filter((wareH) =>
												["1001-16A8", "1001-Display", "1001-9504"].includes(
													wareH.warehouse_name
												)
											)
											.map((wareH) => ({
												warehouse_id: wareH.warehouse_id,
												warehouse_name: wareH.warehouse_name,
												warehouse_stock: wareH.warehouse_available_for_sale_stock,
											})) || [],
									item_tax_preferences: item.item_tax_preferences,
									updatedAt: new Date(),
								},
								$setOnInsert: {
									createdAt: new Date(), // Only set on first insert
									createdBy: "zoho",
									isDeleted: false,
								},
							},
							upsert: true,
						},
					}));

					const updateRes = await db.collection("products").bulkWrite(bulkOps);
					logger.info(`Updated Count: ${updateRes.modifiedCount}`);
					logger.info(`Updated tax and pricing for item_ids: ${itemIds}`);
				}

				// Update the last_modified_time in the database
				const latestModifiedTime = formatDateToIST(new Date());
				await db
					.collection("salesOrders")
					.updateOne(
						{ name: "last_modified_time" },
						{ $set: { last_modified_time: latestModifiedTime } },
						{ upsert: true }
					);
				logger.info(`Updated last_modified_time in salesOrders: ${latestModifiedTime}`);
				logger.info("Completed updating products using last_modified_time");
			} catch (error) {
				logger.error("Error in lastModifiedProducts cron:", error);
			}
		},
		/**
		 * Retrieves tax and pricing information from Zoho and updates the database.
		 *
		 * This function fetches the latest product details from Zoho, including stock, pricing,
		 * and tax information. It processes the data in batches, updates the MongoDB database,
		 * and ensures only recently modified products are fetched using `last_modified_time`.
		 *
		 * @async
		 * @function lastModifiedProducts
		 * @throws {Error} Logs and throws an error if the operation fails.
		 */
		lastModifiedProductsV2: async function () {
			// Connect to the database
			const db = await Services.MongoHelper.getDatabaseConnection();
			try {
				logger.info("Started lastModifiedProducts cron");

				// Fetch the last modified time from salesOrders collection
				const lastModifiedRecord = await db.collection("salesOrders").findOne({ name: "last_modified_time" });
				let lastModifiedTime =
					lastModifiedRecord?.last_modified_time || new Date(Date.now() - 24 * 60 * 60 * 1000);

				// Convert to "YYYY-MM-DDTHH:mm:ss+0530"
				lastModifiedTime = formatDateToIST(new Date(lastModifiedTime));
				// lastModifiedTime = formatDateToIST(new Date("2025-02-28T16:00:03+0530"));
				logger.info(`Using last_modified_time: ${lastModifiedTime}`);

				// Prepare common Zoho API request headers
				const zohoHeaders = {
					Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
					"Content-Type": "application/json",
				};

				// Fetch all Zoho items using pagination
				let allZohoItems = [];
				let warehouseIds =
					config.env === "staging"
						? ["1846261000002306490", "1846261000000838589", "1846261000002616001"]
						: ["2026911000000252153", "2026911000001292002", "2026911000001416444"];

				for (let warehouse_id of warehouseIds) {
					let page = 1;
					let hasMorePages = true;
					while (hasMorePages) {
						const url = `https://www.zohoapis.in/books/v3/items/bulkfetch?organization_id=${
							config.zoho.organization_id
						}&warehouse_id=${warehouse_id}&page=${page}&per_page=2000&filter_by=Status.Active&sort_column=last_modified_time&sort_order=D&usestate=true&last_modified_time=${encodeURIComponent(
							lastModifiedTime
						)}`;

						const { data } = await axios.get(url, { headers: zohoHeaders });
						// logger.debug(data)
						if (data?.items) {
							allZohoItems.push(...data.items);
						}
						hasMorePages = data?.page_context?.has_more_page || false;
						page++;
					}
				}

				logger.info(`Fetched ${allZohoItems.length} items from Zoho`);

				if (allZohoItems.length === 0) {
					logger.info("No new products to update.");
					return;
				}

				// Process items in batches for updating
				const batchSize = 200;
				for (let i = 0; i < allZohoItems.length; i += batchSize) {
					const batch = allZohoItems.slice(i, i + batchSize);

					// Build a comma-separated list of item_ids
					const itemIds = [
						...new Set(batch.filter((product) => product.item_id).map((product) => product.item_id)),
					].join(",");

					// Fetch item details from Zoho for the current batch
					const detailsUrl = `https://www.zohoapis.in/books/v3/itemdetails?item_ids=${itemIds}&organization_id=${config.zoho.organization_id}`;
					const { data: detailsData } = await axios.get(detailsUrl, {
						headers: zohoHeaders,
					});

					// Prepare bulk update operations based on Zoho item details
					const bulkOps = detailsData.items.map((item) => ({
						updateOne: {
							// Use compound filter to check both item_id and code to prevent duplicates
							filter: {
								$or: [
									{ item_id: item.item_id },  // Try to find by item_id first
									{ code: item.sku }          // Fall back to finding by SKU code
								]
							},
							update: {
								$set: {
									code: item.sku,
									item_id: item.item_id,
									stock: item.available_for_sale_stock || 0, // use available stock from new api
									price: +item.purchase_rate,
									mrp: +item.label_rate,
									discount: +item?.cf_structured_discount || 0, // item.custom_fields.find(field=> field.lael === "cf_structured_discount").value
									hsn_or_sac: item?.hsn_or_sac || "",
									unit: item.unit,
									status: item.status,
									tax_id: item.tax_id || "",
									tax_name: item.tax_name || "",
									item_type: item.item_type,
									tax_exemption_code: item.tax_exemption_code,
									warehouse_name: item?.warehouses?.[0]?.warehouse_name || "",
									project_id: item?.project_id || "",
									template_id: item?.associated_template_id || "",
									documents: item?.documents || [],
									warehouses:
										item?.warehouses
											?.filter((wareH) =>
												["1001-16A8", "1001-Display", "1001-9504"].includes(
													wareH.warehouse_name
												)
											)
											.map((wareH) => ({
												warehouse_id: wareH.warehouse_id,
												warehouse_name: wareH.warehouse_name,
												warehouse_stock: wareH.warehouse_available_for_sale_stock,
											})) || [],
									item_tax_preferences: item.item_tax_preferences,
									updatedAt: new Date(),
								},
								$setOnInsert: {
									createdAt: new Date(), // Only set on first insert
									createdBy: "zoho",
									isDeleted: false,
								},
							},
							upsert: true,
						},
					}));

					const updateRes = await db.collection("products").bulkWrite(bulkOps);
					logger.info(`Updated Count: ${updateRes.modifiedCount}`);
					logger.info(`Updated tax and pricing for item_ids: ${itemIds}`);
				}

				// Update the last_modified_time in the database
				const latestModifiedTime = formatDateToIST(new Date());
				await db
					.collection("salesOrders")
					.updateOne(
						{ name: "last_modified_time" },
						{ $set: { last_modified_time: latestModifiedTime } },
						{ upsert: true }
					);
				logger.info(`Updated last_modified_time in salesOrders: ${latestModifiedTime}`);
				logger.info("Completed updating products using last_modified_time");
			} catch (error) {
				logger.error("Error in lastModifiedProducts cron:", error);
			}
		},
	};
};
