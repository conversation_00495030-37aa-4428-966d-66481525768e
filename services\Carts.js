const { logger } = require("../utils");
const { ObjectId } = require("mongodb");

module.exports = function ({ Services }) {
	return {
		/**
		 * Adds a product to the customer's cart or updates an existing cart
		 */
		addProductToCart: async ({
			customerCRMID,
			addressCRMID,
			architectCRMID = "",
			itemList,
			salesRepPID,
			isAnonymous = false,
			customerId,
		}) => {
			try {
				logger.debug("Starting addProductToCart function");
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				const carts = db.collection("carts");
				// const salesRepPID = "P1101"; // hardcoded for now

				logger.debug(
					`Checking for existing cart for customerCRMID: ${isAnonymous ? customerId : customerCRMID}`
				);
				// Check if cart already exists for the customer
				const existingCart = await carts.findOne({ ...(isAnonymous ? { customerId } : { customerCRMID }) });

				if (existingCart) {
					logger.debug("Existing cart found. Updating cart.");

					// Call the updateProductInCart function to handle the update
					const updateResult = await Services.Carts.updateProductInCart({
						...(isAnonymous ? { customerId } : { cartCRMID: existingCart.cartCRMID }),
						itemList,
						salesRepPID,
						isAnonymous,
					});

					if (updateResult.ok) {
						logger.debug("Cart updated successfully");
						return {
							ok: true,
							message: "Cart updated successfully",
							data: updateResult.data,
						};
					} else {
						logger.debug("Failed to update cart");
						return {
							ok: false,
							message: updateResult.message,
						};
					}
				}

				logger.debug("No existing cart found. Creating new cart in Salesforce");
				const itemListSF = itemList.map((item) => ({
					productName: item.productName,
					quantity: item.quantity,
					category: item.category,
					subCategory: item.subCategory,
				}));
				// If cart doesn't exist and is not anonymous, create a new one in Salesforce
				const sfResponse = await Services.SalesforceHelper.createCart({
					customerCrmId: customerCRMID,
					addressCrmId: addressCRMID,
					architectCrmId: architectCRMID,
					requestType: "cart",
					itemList: itemListSF,
					salesPersonPid: salesRepPID,
				});

				logger.debug(sfResponse);

				// Check if Salesforce cart creation was successful
				if (!sfResponse.responses || sfResponse.responses[0].customCode !== 1) {
					logger.debug("Failed to create cart in Salesforce");
					return { ok: false, message: sfResponse.responses[0].message };
				}

				const cartCRMID = sfResponse.responses[0].CartCrmId;
				logger.debug(`New cart created in Salesforce with ID: ${cartCRMID}`);

				logger.debug("Creating new cart item in local database");
				// Create a new cart item in local database
				const newCartItem = {
					customerCRMID,
					addressCRMID,
					architectCRMID,
					itemList,
					salesRepPID,
					cartCRMID,
					createdAt: new Date(),
					updatedAt: new Date(),
				};

				// Insert new cart item into local database
				const result = await carts.insertOne(newCartItem);
				const cartItem = result.insertedId ? await carts.findOne({ _id: result.insertedId }) : null;

				// Check if local database insertion was successful
				if (!cartItem) {
					logger.debug("Failed to create cart item in database");
					return { ok: false, message: "Failed to create cart item in database" };
				}

				logger.debug("Cart created successfully");
				return { ok: true, message: "Cart created successfully", data: cartItem };
			} catch (error) {
				// Log the error and return a generic error message
				logger.error(`Error in addProductToCart: ${error.message}`);
				return { ok: false, message: "An error occurred while creating the cart" };
			}
		},

		/**
		 * Updates a product in the cart
		 */
		updateProductInCart: async ({ cartCRMID, itemList, salesRepPID, customerId }) => {
			try {
				// const salesRepPID = "P1101"; // hardcoded for now
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				const carts = db.collection("carts");
				const existingCart = await carts.findOne({ cartCRMID });
				const updatedItemList = existingCart.itemList.map((existingItem) => {
					const newItem = itemList.find((item) => item.productName === existingItem.productName);
					if (newItem) {
						// Increase quantity if item already exists
						logger.debug(`Updating quantity for product: ${existingItem.productName}`);
						return {
							...existingItem,
							quantity: newItem.quantity,
							unstructuredDiscount: newItem?.unstructuredDiscount || 0,
						};
					}
					return existingItem;
				});

				// Add new items that don't exist in the current cart
				const newItems = itemList.filter(
					(item) =>
						!existingCart.itemList.some((existingItem) => existingItem.productName === item.productName)
				);
				updatedItemList.push(...newItems);
				const itemListSF = updatedItemList.map((item) => ({
					productName: item.productName,
					quantity: item.quantity,
					category: item.category,
					subCategory: item.subCategory,
				}));
				// Update cart in Salesforce
				const sfUpdateResponse = await Services.SalesforceHelper.updateCart({
					CartID: cartCRMID,
					itemList: itemListSF,
					salesPersonPid: salesRepPID,
				});

				// Check if Salesforce cart update was successful
				if (sfUpdateResponse.customCode !== 200) {
					logger.debug("Failed to update cart in Salesforce");
					return { ok: false, message: sfUpdateResponse.message };
				}

				logger.debug(`Cart updated in Salesforce with ID: ${cartCRMID}`);

				// Update cart in local database
				const updateResult = await carts.updateOne(
					{ cartCRMID },
					{
						$set: {
							itemList: updatedItemList,
							salesRepPID,
							updatedAt: new Date(),
						},
					}
				);

				if (updateResult.modifiedCount === 0) {
					logger.debug("Failed to update cart in local database");
					return { ok: false, message: "Failed to update cart in local database" };
				}

				// Fetch the updated cart item
				const updatedCartItem = await carts.findOne({ cartCRMID });

				logger.debug("Cart updated successfully");
				return { ok: true, message: "Cart updated successfully", data: updatedCartItem };
			} catch (error) {
				// Log the error and return a generic error message
				logger.error(`Error in updateProductInCart: ${error.message}`);
				return { ok: false, message: "An error occurred while updating the cart" };
			}
		},

		/**
		 * Fetches cart data for a specific customer and populates product information
		 */
		getCartByCustomerId: async ({ customerCRMID, customerId }) => {
			try {
				// Get database connection and collections
				const db = await Services.MongoHelper.getDatabaseConnection();
				const carts = db.collection("carts");
				const products = db.collection("products");

				// Find the cart for the given customer
				const cartItem = await carts.findOne({ ...(customerCRMID ? { customerCRMID } : { customerId }) });
				if (!cartItem) {
					return { ok: true, message: "No cart items found for this customer", data: null };
				}

				// Extract product names from the cart items
				const productNames = cartItem.itemList.map((item) => item.productName);

				// Fetch product details for all products in the cart
				const productDetails = await products.find({ code: { $in: productNames } }).toArray();

				// // Sort productImages for each product in descending order by updatedAt
				// productDetails.forEach(product => {
				// 	if (product.productImages && Array.isArray(product.productImages)) {
				// 		product.productImages.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
				// 	}
				// });

				// Create a map of product names to product details for efficient lookup
				const productMap = new Map(productDetails.map((p) => [p.code, p]));

				// Populate cart items with product details
				const populatedCartItems = cartItem.itemList.map((item) => ({
					...item,
					productDetails: productMap.get(item.productName) || null,
				}));
				cartItem.itemList = populatedCartItems;

				// Return successful response with populated cart items
				return {
					ok: true,
					message: "Cart items fetched successfully",
					data: cartItem,
				};
			} catch (error) {
				// Log the error and return a generic error message
				logger.error(error);
				return { ok: false, message: "An error occurred while fetching the cart data" };
			}
		},

		/**
		 * Deletes multiple cart items for a customer
		 */
		deleteMultipleCartItems: async ({ productNames, cartCRMID, customerId }) => {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				const carts = db.collection("carts");

				// Find the cart and update it in a single operation
				const result = await carts.findOneAndUpdate(
					{ ...(cartCRMID ? { cartCRMID } : { customerId }) },
					{
						$pull: { itemList: { productName: { $in: productNames } } },
						$set: { updatedAt: new Date() },
					},
					{ returnDocument: "after" }
				);

				if (!result) {
					return { ok: false, message: "Cart not found" };
				}
				// If customerId is not provided, update the cart in Salesforce
				if (!customerId) {
					// Update cart in Salesforce
					const sfUpdateResponse = await Services.SalesforceHelper.updateCart({
						CartID: cartCRMID,
						itemList: result.itemList.map((item) => {
							const { unstructuredDiscount, warehouse_ids, ...rest } = item;
							return rest;
						}),
						salesPersonPid: result.salesRepPID,
					});
					logger.debug(`Salesforce update response: ${sfUpdateResponse.message}`);

					logger.debug(`Successfully deleted ${productNames.length} items from cart ${cartCRMID}`);
				}

				return {
					ok: true,
					message: `Successfully deleted cart items`,
					deletedCount: productNames.length,
				};
			} catch (error) {
				logger.error(error);
				return { ok: false, message: error.message };
			}
		},

		/**
		 * Updates the unstructured discount or warehouse_id in the cart
		 */
		updateUnstructuredDiscountWarehouseIdComment: async ({
			cartCRMID,
			unstructuredDiscount,
			warehouse_ids,
			code,
			comment,
		}) => {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				const carts = db.collection("carts");

				// Initialize update object with updatedAt timestamp
				const updateObj = {
					$set: { updatedAt: new Date() },
				};

				// Handle comments update if provided
				if (comment) {
					const existingCart = await carts.findOne({ cartCRMID });
					const existingComments = existingCart?.comments || {};
					updateObj.$set.comments = {
						...existingComments,
						...comment,
					};
				}

				// Handle unstructured discount update
				if (unstructuredDiscount !== undefined && code) {
					updateObj.$set["itemList.$[elem].unstructuredDiscount"] = unstructuredDiscount;
				}

				// Handle warehouse IDs update
				if (warehouse_ids !== undefined && code) {
					updateObj.$set["itemList.$[elem].warehouse_ids"] = warehouse_ids;
				}

				// Update cart document
				const result = await carts.findOneAndUpdate({ cartCRMID }, updateObj, {
					arrayFilters: code ? [{ "elem.productName": code }] : undefined,
					returnDocument: "after",
				});

				if (!result) {
					return { ok: false, message: "Cart not found" };
				}

				return {
					ok: true,
					message: "Cart item updated successfully",
					data: result,
				};
			} catch (error) {
				logger.error(error);
				return { ok: false, message: "An error occurred while updating the cart item" };
			}
		},
	};
};
