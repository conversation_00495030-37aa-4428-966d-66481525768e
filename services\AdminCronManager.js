var CronJob = require("cron").CronJob;
const { logger } = require("../utils");
let hashMap = {};

// cron manager class which helps manage all cron jobs
class CronManagerClass {
	constructor() {}
	/**
	 * addJob
	 * adds a javascript function as a cron job for the given cron schedule
	 */
	addJob(tagDesc, schedule, fn) {
		try {
			hashMap[tagDesc] = {
				cronJobObj: new CronJob(schedule, fn, null, true, "Asia/Kolkata"),
				schedule,
			};
			logger.info("Job Added", "TAG:", `"${tagDesc}"`, "SCHEDULE:", `"${schedule}"`);
		} catch (e) {
			logger.error("Manager threw an Error", e.message);
		}
	}
	/**
	 * removeJob
	 * removes a cron job
	 */
	removeJob(tagDesc) {
		if (hashMap.hasOwnProperty(tagDesc)) {
			hashMap[tagDesc].cronJobObj.stop();
			delete hashMap[tagDesc];
			logger.info("Job Removed", tagDesc);
		}
	}
	/**
	 * listJobs
	 * lists all crons jobs that have been loaded
	 */
	listJobs() {
		Object.keys(hashMap).forEach((key) => {
			logger.info("TAG:", key, "SCHEDULE:", hashMap[key].schedule);
		});
	}
	/**
	 * removeAllJobs
	 * removes all loaded cron jobs
	 */
	removeAllJobs() {
		Object.keys(hashMap).forEach((key) => {
			hashMap[key].cronJobObj.stop();
			delete hashMap[key];
			logger.info("Job Removed", key);
		});
	}
}

module.exports = ({ Services }) => {
	let cronManagerInstance = new CronManagerClass();

	return {
		/**
		 * Services.AdminCronManager.assertCronJobs
		 * checks for cron jobs from the collection in the database
		 * removes crons which have been disabled
		 * add new crons which have been added or now are enabled in the database
		 */
		assertCronJobs: async function () {
			const db = Services.MongoHelper.getDatabaseConnection();
			// load all the cron jobs from the "cronSchedules" collection
			let schedules = await db.collection("cronSchedules").find().toArray();
			if (schedules.length == 0) logger.info("No Cron Jobs to assert");
			for (let schedule of schedules) {
				// forcefully remove the loaded cron if any
				cronManagerInstance.removeJob(schedule.tag);

				// if the cron job is enabled then load it.
				if (schedule.enabled == 1) {
					cronManagerInstance.addJob(
						schedule.tag,
						schedule.cronExpression,
						schedule.serviceFunctionPath.split(".").reduce((o, i) => o[i], Services)
					);
				}
			}
		},
	};
};
