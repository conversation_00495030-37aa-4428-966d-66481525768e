/**
 * UserAnalytics Controller
 * Provides endpoints for user-level analytics
 */

const { logger } = require("../utils");
const { Parser } = require('json2csv');

module.exports.routes = function ({ Services, config }) {
    return {
        /**
         * Get User Analytics
         * This route provides user-level analytics data
         */
        "GET /": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalytics(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Export User Analytics as CSV
         * This route provides user-level analytics data in CSV format
         */
        "GET /export": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalytics(options);
                    if (!data.ok) {
                        return res.status(500).json({ ok: false, message: data.message });
                    }

                    // Define fields for CSV
                    const fields = [
                        { label: 'Name', value: 'name' },
                        { label: 'Employee Code', value: 'pcode' },
                        { label: 'Login Count', value: 'loginCount' },
                        { label: 'Customers Created', value: 'customersCreated' },
                        { label: 'Quotes Created', value: 'quotesCreated' },
                        { label: 'Total Quote Value', value: 'totalQuoteValue' },
                        { label: 'Sales Orders Created', value: 'salesOrdersCreated' },
                        { label: 'Total Sales Order Value', value: 'totalSalesOrderValue' }
                    ];

                    // Create CSV parser
                    const json2csvParser = new Parser({ fields });
                    const csv = json2csvParser.parse(data.data);

                    // Set headers for CSV download
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', 'attachment; filename=user-analytics.csv');

                    // Send CSV data
                    return res.send(csv);
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Get User Analytics for a specific user
         * This route provides user-level analytics data for a specific user
         */
        "GET /:employeecode": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { employeecode } = req.params;
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {
                        employeecode
                    };

                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalyticsByEmployeeCode(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Get Date-wise Analytics
         * This route provides date-wise analytics data
         */
        "GET /date-wise": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate, employeecode } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }
                    if (employeecode) {
                        options.employeecode = employeecode;
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalytics(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Export Date-wise Analytics as CSV
         * This route provides date-wise analytics data in CSV format
         */
        "GET /date-wise/export": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate, employeecode } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }
                    if (employeecode) {
                        options.employeecode = employeecode;
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalytics(options);
                    if (!data.ok) {
                        return res.status(500).json({ ok: false, message: data.message });
                    }

                    // Define fields for CSV
                    const fields = [
                        { label: 'Date', value: 'date' },
                        { label: 'Number of Quotes', value: 'quotesCount' },
                        { label: 'Total Quote Value', value: 'totalQuoteValue' },
                        { label: 'Number of Sales Orders', value: 'salesOrdersCount' },
                        { label: 'Total Sales Order Value', value: 'totalSalesOrderValue' }
                    ];

                    // Create CSV parser
                    const json2csvParser = new Parser({ fields });
                    const csv = json2csvParser.parse(data.data);

                    // Set headers for CSV download
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', 'attachment; filename=date-wise-analytics.csv');

                    // Send CSV data
                    return res.send(csv);
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Get Date-wise Analytics
         * This route provides date-wise analytics data
         */
        "GET /date-wise/singleUser": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate, employeecode } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }
                    if (employeecode) {
                        options.employeecode = employeecode;
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalytics(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Export Date-wise Analytics as CSV
         * This route provides date-wise analytics data in CSV format
         */
        "GET /date-wise/singleUser/export": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate, employeecode } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }
                    if (employeecode) {
                        options.employeecode = employeecode;
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalytics(options);
                    if (!data.ok) {
                        return res.status(500).json({ ok: false, message: data.message });
                    }

                    // Define fields for CSV
                    const fields = [
                        { label: 'Date', value: 'date' },
                        { label: 'Number of Customers Created', value: 'customersCount' },
                        { label: 'Number of Quotes', value: 'quotesCount' },
                        { label: 'Total Quote Value', value: 'totalQuoteValue' },
                        { label: 'Number of Sales Orders', value: 'salesOrdersCount' },
                        { label: 'Total Sales Order Value', value: 'totalSalesOrderValue' }
                    ];

                    // Create CSV parser
                    const json2csvParser = new Parser({ fields });
                    const csv = json2csvParser.parse(data.data);

                    // Set headers for CSV download
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', 'attachment; filename=date-wise-analytics.csv');

                    // Send CSV data
                    return res.send(csv);
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Get Date-wise Analytics for All Users Combined
         * This route provides date-wise analytics data for all users combined
         */
        "GET /date-wise/all-users": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalyticsAllUsers(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Export Date-wise Analytics for All Users as CSV
         * This route provides date-wise analytics data for all users in CSV format
         */
        "GET /date-wise/all-users/export": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getDateWiseAnalyticsAllUsers(options);
                    if (!data.ok) {
                        return res.status(500).json({ ok: false, message: data.message });
                    }

                    // Define fields for CSV
                    const fields = [
                        { label: 'Date', value: 'date' },
                        { label: 'Number of Customers Created', value: 'customersCount' },
                        { label: 'Number of Quotes', value: 'quotesCount' },
                        { label: 'Total Quote Value', value: 'totalQuoteValue' },
                        { label: 'Number of Sales Orders', value: 'salesOrdersCount' },
                        { label: 'Total Sales Order Value', value: 'totalSalesOrderValue' }
                    ];

                    // Create CSV parser
                    const json2csvParser = new Parser({ fields });
                    const csv = json2csvParser.parse(data.data);

                    // Set headers for CSV download
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', 'attachment; filename=date-wise-analytics-all-users.csv');

                    // Send CSV data
                    return res.send(csv);
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },
    };
};
