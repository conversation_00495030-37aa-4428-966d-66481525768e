const { MongoGridFSChunkError } = require("mongodb");
const config = require("../config/conf");
const mongoConnector = require("../helpers/mongoConnector");

module.exports.emailValidator = (email) => {
	const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
	if (emailRegex.test(email)) {
		return true;
	} else {
		return false;
	}
};

module.exports.mobileValidator = (mobile) => {
	const mobileRegex = /^[0-9]{10}$/;
	if (mobileRegex.test(mobile)) {
		return true;
	} else {
		return false;
	}
};

module.exports.formatDateToIST = (date) => {
	const offset = 5.5 * 60; // +05:30 in minutes
	const localDate = new Date(date.getTime() + offset * 60 * 1000);
	const isoString = localDate.toISOString().slice(0, 19); // Remove milliseconds
	return `${isoString}+0530`; // Append "+0530" manually
};

module.exports.generateNonExistingMobileNumber = async () => {
	// Establish database connection
	const db = await mongoConnector({ mongo: config.mongo });

	// Initialize ZohoHelper service
	const Services = {
		SalesforceHelper: await require("../services/SalesforceHelper")({ Services: {}, config }),
	};

	// Get the mobile number counter document from the database
	let mobileCounter = await db.collection("miscellaneous").findOne({ name: "mobileCounter" });

	// Construct the mobile number using the current counter value
	let mobile = `+91${mobileCounter.value}`;

	// Fetch the customer using the current mobile number
	let customerExists = await Services.SalesforceHelper.fetchCustomer({ mobile });

	// Loop until we get a response indicating that the customer doesn't exist (customCode 400)
	while (customerExists.customCode === 200) {
		// Increment the counter value
		mobileCounter.value += 1;

		// Update the counter in the database
		await db
			.collection("miscellaneous")
			.updateOne({ name: "mobileCounter" }, { $set: { value: mobileCounter.value } });

		// Update the mobile number with the new counter value
		mobile = `+91${mobileCounter.value}`;

		// Try fetching the customer again with the new mobile number
		customerExists = await Services.SalesforceHelper.fetchCustomer({ mobile });
	}

	// Return the non-existing mobile number
	return mobile;
};

// non existing mobile response customerExists: {"name":null,"mobile":null,"message":"Customer Not Found","email":null,"customerid":null,"customCode":400,"Address":null}
// // existing mobile response
// const customerExists = {
// 	name: "Rajeev Kumar",
// 	mobile: "+919304302979",
// 	message: "Nilaya Exisiting Customer",
// 	email: "<EMAIL>",
// 	customerid: "5161",
// 	customCode: 200,
// 	Address: [
// 		{ Street: "Harmu", Pincode: "400003", Name: "2195", City: "Mumbai" },
// 		{ Street: "83/7 chitrakoot enclave", Pincode: "248001", Name: "2196", City: "dehradun" },
// 		{ Street: "v", Pincode: "834005", Name: "2656", City: "Ranchi" },
// 	],
// };
