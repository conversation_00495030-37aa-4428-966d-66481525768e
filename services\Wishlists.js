const { ObjectId } = require("mongodb");
const { logger } = require("../utils");

module.exports = async function ({ config, Services }) {
	return {
		/**
		 * Creates a new wishlist for a customer
		 */
		createWishlist: async ({
			wishlistName,
			customerCRMID,
			addressCRMID,
			architectCRMID = "",
			itemList,
			salesRepPID,
		}) => {
			// Get the database connection
			const db = await Services.MongoHelper.getDatabaseConnection();

			// Check if the wishlist exists for the customer with the same name
			const existingWishlist = await db.collection("wishlists").findOne({ customerCRMID, wishlistName });
			if (existingWishlist) {
				return { ok: false, message: `Wishlist with name ${wishlistName} already exists` };
			}

			// Create new wishlist in our database
			const wishlist = await db.collection("wishlists").insertOne({
				customerCRMID,
				addressCRMID,
				architectCRMID,
				wishlistName,
				itemList,
				salesRepPID,
				createdAt: new Date(),
				updatedAt: new Date(),
			});

			// Call Salesforce to create wishlist
			const sfData = await Services.SalesforceHelper.createWishlist({
				customerCrmId: customerCRMID,
				addressCrmId: addressCRMID,
				architectCrmId: architectCRMID,
				requestType: "wishlist",
				itemList,
				salesPersonPid: salesRepPID,
			});
			logger.debug(sfData);

			// If Salesforce successfully created the wishlist, update our database with the Salesforce wishlist ID
			if (sfData.responses[0].customCode === 1) {
				await db
					.collection("wishlists")
					.updateOne(
						{ _id: wishlist.insertedId },
						{ $set: { wishlistCRMID: sfData.responses[0].wishlistCrmId } }
					);
			}

			// Return success response with the created wishlist data
			return { ok: true, data: wishlist };
		},
		removeWishlist: async ({ wishlistId }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const result = await db
				.collection("wishlists")
				.deleteOne({ _id: ObjectId.createFromHexString(wishlistId) });
			if (result.deletedCount === 0) {
				return { ok: false, message: "Wishlist not found" };
			}
			return { ok: true, message: "Wishlist removed successfully" };
		},

		getWishlist: async ({ customerCRMID }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();

			const wishlistItems = await db
				.collection("wishlists")
				.aggregate([
					{ $match: { customerCRMID } },
					// Step 1: Conditionally unwind itemList
					{
						$facet: {
							nonEmptyWishlists: [
								{ $match: { "itemList.0": { $exists: true } } },
								{ $unwind: "$itemList" },
								{
									$lookup: {
										from: "products",
										localField: "itemList.productName",
										foreignField: "code",
										as: "productDetails",
									},
								},
								{
									$addFields: {
										"itemList.productDetails": { $arrayElemAt: ["$productDetails", 0] },
									},
								},
								{
									$group: {
										_id: "$_id",
										customerCRMID: { $first: "$customerCRMID" },
										addressCRMID: { $first: "$addressCRMID" },
										architectCRMID: { $first: "$architectCRMID" },
										wishlistName: { $first: "$wishlistName" },
										itemList: { $push: "$itemList" },
										salesRepPID: { $first: "$salesRepPID" },
										createdAt: { $first: "$createdAt" },
										updatedAt: { $first: "$updatedAt" },
										wishlistCRMID: { $first: "$wishlistCRMID" },
									},
								},
							],
							emptyWishlists: [{ $match: { itemList: { $size: 0 } } }],
						},
					},
					// Combine non-empty and empty wishlists
					{
						$project: {
							allWishlists: {
								$concatArrays: ["$nonEmptyWishlists", "$emptyWishlists"],
							},
						},
					},
					{ $unwind: "$allWishlists" },
					{ $replaceRoot: { newRoot: "$allWishlists" } },
					{
						$sort: { createdAt: -1 },
					},
				])
				.toArray();

			return { ok: true, data: wishlistItems };
		},

		updateWishlist: async ({ wishlistCRMID, itemList, salesRepPID }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();

			const result = await db
				.collection("wishlists")
				.updateOne({ wishlistCRMID }, { $set: { itemList, salesRepPID } });
			if (result.modifiedCount === 0) {
				return { ok: false, message: "Wishlist not found" };
			}
			// Update wishlist in Salesforce
			const sfData = await Services.SalesforceHelper.updateWishlist({
				WISHLISTID: wishlistCRMID,
				itemList,
				salesPersonPid: salesRepPID,
			});

			if (sfData.customCode !== 200) {
				logger.error("Failed to update wishlist in Salesforce:", sfData.message);
				return { ok: false, message: "Failed to update wishlist in Salesforce" };
			}
			return { ok: true, message: "Wishlist updated successfully" };
		},
	};
};
