/**
 * Wishlists Controller
 * This module handles all wishlist-related routes and operations
 */

// Import the logger utility for logging purposes
const { logger } = require("../utils");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Create Wishlist
		 * This route is used to create a new wishlist for the customer
		 */
		"POST /create": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Extract required fields from the request body
					const { wishlistName, customerCRMID, addressCRMID, architectCRMID, itemList, salesRepPID } =
						req.body;

					// Validate that all required fields are present
					if (
						!wishlistName ||
						!customerCRMID ||
						!itemList ||
						!salesRepPID
					) {
						return res.json({ ok: false, message: "All fields are required" });
					}

					// Call the Wishlists service to create a new wishlist
					const data = await Services.Wishlists.createWishlist({
						wishlistName,
						customerCRMID,
						addressCRMID,
						architectCRMID,
						itemList,
						salesRepPID,
						requestType: "wishlist",
					});

					// If wishlist creation was successful, return success response
					if (data.ok) {
						return res.json({ ok: true, message: "Wishlist created successfully", data: data.data });
					}

					// If wishlist creation failed, return error response
					res.json({ ok: false, message: data.message });
				} catch (e) {
					// If an exception occurs, log the error and return a generic error message
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Remove wishlist
		 * This route is used to remove a wishlist
		 */
		"DELETE /remove/:wishlistId": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { wishlistId } = req.params;
					if (!wishlistId) {
						return res.json({ ok: false, message: "Wishlist ID is required" });
					}
					const data = await Services.Wishlists.removeWishlist({ wishlistId });
					if (data.ok) {
						return res.json({ ok: true, message: data.message });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get Wishlist
		 * This route is used to fetch a customer's wishlist
		 */
		"GET /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { customerCRMID } = req.query;
					if (!customerCRMID) {
						return res.json({ ok: false, message: "Customer CRM ID is required" });
					}
					const data = await Services.Wishlists.getWishlist({ customerCRMID });
					if (data.ok) {
						return res.json({ ok: true, data: data.data });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Update Wishlist
		 * This route is used to update a wishlist
		 */
		"POST /update": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Extract required parameters from the request body
					const { wishlistCRMID, itemList, salesRepPID } = req.body;

					// Validate that all required parameters are present
					if (!wishlistCRMID || !itemList || !salesRepPID) {
						return res.json({
							ok: false,
							message: "Wishlist CRM ID, Item List, and Sales Rep PID are required",
						});
					}

					// Call the Wishlists service to update the wishlist
					const data = await Services.Wishlists.updateWishlist({ wishlistCRMID, itemList, salesRepPID });

					// If the update was successful, return a success response
					if (data.ok) {
						return res.json({ ok: true, message: data.message });
					}

					// If the update failed, return an error response
					res.json({ ok: false, message: data.message });
				} catch (e) {
					// If an exception occurs, log the error and return a generic error message
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},
	};
};
