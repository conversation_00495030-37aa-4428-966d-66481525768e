const axios = require("axios");
const { logger } = require("../utils");

module.exports = async function ({ Services, config }) {
	return {
		updateProductStock: async function ({ sku }) {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				// get the product from the database
				const product = await db.collection("products").findOne({ code: sku });

				// get the product details from zoho
				const response = await Services.ZohoHelper.getProductStockAndPrice({ item_id: product.item_id });

				if (response.item.sku === sku) {
					const item = response.item;
					const updatedProduct = await db.collection("products").updateOne(
						{ code: sku },
						{
							$set: {
								stock: item.available_for_sale_stock,
								price: item.purchase_rate,
								mrp: item.label_rate,
								discount: item.custom_field_hash.cf_structured_discount,
								hsn_or_sac: item.hsn_or_sac,
								warehouses:
									item?.warehouses?.map((warehouse) => {
										return {
											warehouse_id: warehouse.warehouse_id,
											warehouse_name: warehouse.warehouse_name,
											warehouse_stock: warehouse.warehouse_available_for_sale_stock,
										};
									}) || [],
							},
						}
					);
					logger.debug(updatedProduct);
					logger.info(
						`Updated product ${sku} with stock: ${item.available_for_sale_stock} and price: ${item.purchase_rate}`
					);
				}
			} catch (error) {
				logger.error("Error updating product stock:", error);
				throw error;
			}
		},
		contactSearch: async function ({ mobile }) {
			try {
				const response = await axios({
					method: "GET",
					url: `${config.zoho.booksBaseUrl}/api/v3/customers?organization_id=${config.zoho.organization_id}&phone_contains=${mobile}`,
					headers: {
						Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
					},
				});
				return response.data;
			} catch (error) {
				logger.error("Error getting customer in Zoho:", error);
				throw error;
			}
		},
		createContact: async function (payload) {
			try {
				const response = await axios({
					method: "POST",
					url: `https://www.zohoapis.in/books/v3/contacts?organization_id=${config.zoho.organization_id}`,
					headers: {
						Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
						"Content-Type": "application/json",
					},
					data: payload,
				});
				return response.data;
			} catch (error) {
				logger.error("Error creating contact in Zoho:", error.message);
				throw error;
			}
		},
		createSalesOrder: async function ({
			customer_id,
			contact_persons,
			shipment_date,
			is_inclusive_tax,
			line_items,
			custom_fields,
			notes,
			terms,
			tax_exemption_code,
			tax_authority_name,
			pricebook_id,
			documents,
			gst_treatment,
			gst_no,
			totalAmount,
			payment_terms,
			quoteCRMIDs,
			products,
			shipping_address_id,
			billing_address_id,
			place_of_supply,
			isExportCustomer = false,
		}) {
			const db = await Services.MongoHelper.getDatabaseConnection();

			let salesorder_number = 0;

			// check if all the products are of service category
			let category = "goods";
			products.forEach((product) => {
				if (product.category === "SERVICES") {
					category = "service";
				}
			});

			// get the latest salesorder number
			const salesOrderNoObj = await db.collection("salesOrders").findOne({ name: category });
			// increment the salesorder number
			salesOrderNoObj.number = salesOrderNoObj.number + 1;
			// create salesorder number for payload
			salesorder_number = `${salesOrderNoObj.prefix}-${salesOrderNoObj.number.toString().padStart(5, "0")}`;

			const date = new Date();
			const formattedDate = date.toISOString().split("T")[0];
			const data = {
				customer_id,
				...(contact_persons && { contact_persons }),
				date: formattedDate,
				...(shipment_date && { shipment_date }),
				...(is_inclusive_tax && { is_inclusive_tax }),
				...(custom_fields && { custom_fields }),
				line_items,
				...(notes && { notes }),
				...(terms && { terms }),
				is_discount_before_tax: true,
				discount_type: "multi_discount",
				adjustment: "",
				...(tax_exemption_code && { tax_exemption_code }),
				...(tax_authority_name && { tax_authority_name }),
				adjustment_description: "Adjustment",
				...(pricebook_id && { pricebook_id }),
				template_id: "",
				...(documents && documents.length > 0 && { documents }),
				...(shipping_address_id && { shipping_address_id }),
				...(billing_address_id && { billing_address_id }),
				...(gst_treatment && { gst_treatment: isExportCustomer ? "overseas" : gst_treatment }),
				...(gst_no && !isExportCustomer && { gst_no }),
				place_of_supply: isExportCustomer ? "" : place_of_supply,
				payment_terms: payment_terms || 0,
				payment_terms_label: "Due on Receipt",
				is_adv_tracking_in_package: true,
				is_tcs_amount_in_percent: true,
				order_status: "confirmed",
				reference_number: quoteCRMIDs.join(","),
				salesorder_number,
			};
			logger.debug(`soPayload: ${JSON.stringify(data)}`);
			const response = await axios({
				method: "POST",
				url: `https://www.zohoapis.in/books/v3/salesorders?organization_id=${config.zoho.organization_id}`,
				headers: {
					"Content-Type": "application/json",
					Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
				},
				data: data,
			});
			// update salesorder number
			await db
				.collection("salesOrders")
				.updateOne({ name: category }, { $set: { number: salesOrderNoObj.number } });
			return response.data;
		},

		getProductStockAndPrice: async function ({ sku, page, per_page, item_id }) {
			try {
				const response = await axios({
					method: "GET",
					url: `https://www.zohoapis.in/books/v3/items${item_id ? `/${item_id}` : ""}?organization_id=${
						config.zoho.organization_id
					}${sku ? `&sku=${sku}` : ""}${page ? `&page=${page}` : ""}${
						per_page ? `&per_page=${per_page}` : ""
					}`,
					headers: {
						Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error updating product stock:", error);
				throw error;
			}
		},

		/**
		 * Create Contact in ZOHO
		 * Create a contact in Zoho after getting customerCRMID from Sf
		 * @param {Object} param0 - The customer details
		 * @param {string} param0.mobile - The mobile number of the customer
		 * @param {string} param0.name - The name of the customer
		 * @param {string} param0.email - The email of the customer
		 */
		createContactDuringRegistration: async function ({ mobile, name, email, addressFromPayload = {}, gst_treatment = "consumer", isExportCustomer = false }) {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				const customer = await db.collection("customers").findOne({ mobile: mobile.toString() });
				let address = await db.collection("addresses").findOne({ customerId: customer._id.toString() });
				if (Object.keys(addressFromPayload).length > 0) {
					address = addressFromPayload;
				}
				
				// Ensure address object exists with default values
				if (!address) {
					logger.warn(`No address found for customer ${customer._id}, using default address`);
					address = {};
				}
				
				// Set GST treatment based on customer type
				const finalGstTreatment = isExportCustomer ? "overseas" : gst_treatment;
				
				// Handle address for overseas customers
				if (isExportCustomer === true) {
					// Export customers: force code as 99 and region as DM
					address = {
						...address,
						state: address?.state || "Overseas",
						country: address?.country || "OVERSEAS", 
						city: address?.city || "Overseas",
						region: "DM",
						code: "99",
						pincode: address?.pincode || "000000",
						houseNo: address?.houseNo || "",
						street: address?.street || ""
					};
				} else {
					// Non-export customers: cannot have code 99 and region DM
					// If they currently have 99/DM, change to preferred values MH/27
					let region = address?.region;
					let code = address?.code;
					
					// If only region is DM, change it to MH
					if (region === 'DM' || code === '99') {
						region = 'MH';
						code = '27';
					}
					// If no region/code provided, set preferred defaults for non-export customers
					else if (!region && !code) {
						region = 'MH';
						code = '27';
					}
					
					// For domestic customers, ensure required fields exist
					address = {
						...address,
						state: address?.state || "Maharashtra",
						country: address?.country || "India",
						city: address?.city || "Mumbai",
						region: region || "MH",
						code: code || "27",
						pincode: address?.pincode || "400013",
						houseNo: address?.houseNo || "",
						street: address?.street || ""
					};
				}
				
				const place_of_contact = await db.collection("stateDescriptions").findOne({
					Description: { $regex: new RegExp(address.state, "i") },
				});
				const payload = {
					contact_name: name,
					company_name: "",
					website: "",
					contact_type: "customer",
					currency_id: "1846261000000000064", // hardcoded for now need clarification
					payment_terms: 0,
					payment_terms_label: "Due On Receipt",
					credit_limit: 0,
					pricebook_id: "",
					notes: "",
					billing_address: {
						attention: "",
						address: `${address.houseNo}`, //address line 1
						country: isExportCustomer ? (address.country || "OVERSEAS") : "India", // Use address country for export customers
						street2: address.street, //address line 2
						city: address.city,
						state: address.state,
						zip: address.pincode, //pincode
						fax: "",
						phone: "",
					}, //Pass the billing address value
					shipping_address: {
						attention: "",
						address: `${address.houseNo}`, //pass the biling address value
						country: isExportCustomer ? (address.country || "OVERSEAS") : "India", // Use address country for export customers
						street2: address.street,
						city: address.city,
						state: address.state,
						zip: address.pincode,
						fax: "",
						phone: "",
					},
					contact_persons: [
						{
							first_name: name,
							last_name: "",
							mobile: mobile,
							phone: "",
							email: email,
							salutation: "",
							is_primary_contact: true,
							skype: "",
							designation: "",
							department: "",
							communication_preference: {
								is_email_enabled: true,
								is_sms_enabled: true,
							},
						},
					],
					default_templates: {
						estimate_template_id: "",
						invoice_template_id: "",
						creditnote_template_id: "",
						salesorder_template_id: "",
						retainerinvoice_template_id: "",
						paymentthankyou_template_id: "",
						retainerinvoice_paymentthankyou_template_id: "",
						statement_template_id: "",
						estimate_email_template_id: "",
						invoice_email_template_id: "",
						creditnote_email_template_id: "",
						salesorder_email_template_id: "",
						retainerinvoice_email_template_id: "",
						paymentthankyou_email_template_id: "",
						retainerinvoice_paymentthankyou_email_template_id: "",
					},
					is_portal_enabled: false,
					custom_fields: [
						{
							label: "Foyr Customer Code",
							value: customer?.architectCRMID || customer?.customerCRMID,
						},
						{
							label: "SF Customer Id", // hardcoded for now need clarification
							value: customer?.architectCRMID || customer?.customerCRMID,
						},
						{
							label: "Custom Region", //Region
							value: address?.region || (isExportCustomer ? "DM" : place_of_contact?.Region || ""),
						},
						{
							label: "State Code", //state Code
							value: address?.code || (isExportCustomer ? "99" : place_of_contact?.["State Code"] || ""),
						},
					],
					is_taxable: true,
					owner_id: "",
					language_code: "en",
					tags: [],
					twitter: "",
					facebook: "",
					ach_supported: false,
					gst_no: "",
					gst_treatment: finalGstTreatment,
					place_of_contact: place_of_contact?.ZohoStateCode || "",
					pan_no: "", // hardcoded for now need clarification
					customer_sub_type: "individual",
					opening_balances: [
						{
							opening_balance_amount: "",
							exchange_rate: 1,
							location_id: "1846261000000060014", // hardcoded for now need clarification
						},
					],
					legal_name: "",
					trader_name: "",
					documents: [],
					msme_type: "",
					udyam_reg_no: "",
				};

				logger.debug(JSON.stringify(payload));
				// Search for the customer in Zoho using their mobile number
				let zohoCustomerExists;
				try {
					zohoCustomerExists = await Services.ZohoHelper.contactSearch({ mobile });
					logger.debug(zohoCustomerExists);
				} catch (error) {
					logger.error("Error searching for customer in Zoho:", error);
					// If search fails, assume customer doesn't exist and proceed with creation
					zohoCustomerExists = { contacts: [] };
				}

				// Ensure contacts property exists and is an array
				if (!zohoCustomerExists || !Array.isArray(zohoCustomerExists.contacts)) {
					logger.warn("Unexpected response structure from Zoho contact search, assuming no existing contacts");
					zohoCustomerExists = { contacts: [] };
				}

				if (zohoCustomerExists.contacts.length > 0) {
					// If customer exists in Zoho, update the local database with the Zoho contact ID
					logger.info(`Customer with mobile ${mobile} already exists in Zoho`);
					await db
						.collection("customers")
						.updateOne({ mobile }, { $set: { customer_id: zohoCustomerExists.contacts[0].contact_id } });
				} else {
					// If customer doesn't exist in Zoho, create a new contact in Zoho
						const zohoCustomer = await Services.ZohoHelper.createContact(payload);
						logger.debug(zohoCustomer);

						// Update the local database with the new Zoho contact ID
						await db
							.collection("customers")
							.updateOne({ mobile }, { $set: { customer_id: zohoCustomer.contact.contact_id } });
						logger.info(
							`Customer with mobile ${mobile} created in Zoho with contact_id:${zohoCustomer.contact.contact_id}`
						);
						return zohoCustomer;
					
				}
			} catch (error) {
				// Log any errors that occur during the process
				logger.error("Error creating contact in Zoho:", error);
				throw error;
			}
		},

		updateContact: async function ({ customer, address, isExportCustomer = false }) {
			try {
				// Get database connection and lookup the state document
				const db = await Services.MongoHelper.getDatabaseConnection();	
				
				// Set GST treatment based on customer type
				const gstTreatment = isExportCustomer ? "overseas" : "consumer";
				
				// Handle address and state lookup for overseas customers
				let stateDoc = null;
				let finalAddress = { ...address };

				// Apply business logic for export customers
				if (isExportCustomer === true) {
					// Export customers: force code as 99 and region as DM
					finalAddress = {
						...address,
						state: address?.state || "Overseas",
						country: address?.country || "OVERSEAS",
						city: address?.city || "Overseas", 
						region: "DM",
						code: "99",
						pincode: address?.pincode || "000000",
						houseNo: address?.houseNo || "",
						street: address?.street || ""
					};
				} else {
					// Non-export customers: cannot have code 99 and region DM
					// If they currently have 99/DM, change to preferred values MH/27
					// If only region is DM, change it to MH
					if (finalAddress.region === 'DM' || finalAddress.code === '99') {
						finalAddress.region = 'MH';
						finalAddress.code = '27';
					}
					// If no region/code provided, set preferred defaults for non-export customers
					else if (!finalAddress.region && !finalAddress.code) {
						finalAddress.region = 'MH';
						finalAddress.code = '27';
					}
					
					// For domestic customers, lookup state document
					stateDoc = await db
						.collection("stateDescriptions")
						.findOne({ Description: { $regex: new RegExp(address.state, "i") } });
				}

				// Create a reusable address payload since billing and shipping are identical
				const addressPayload = {
					address: finalAddress.houseNo,
					country: finalAddress.country || (isExportCustomer ? "OVERSEAS" : "India"), 
					street2: finalAddress.street,
					city: finalAddress.city,
					state: finalAddress.state,
					zip: finalAddress.pincode,
				};

				// Determine the CRM ID (common for both custom fields)
				const commonCRMID = customer.architectCRMID || customer.customerCRMID;

				// Build the payload for the Zoho API update
				const payloadData = {
					contact_name: customer.name,
					contact_type: "customer",
					currency_id: "", // hardcoded for now; need clarification
					payment_terms: 0,
					payment_terms_label: "Due On Receipt",
					credit_limit: 0,
					custom_fields: [
						// { label: "Foyr Customer Code", value: commonCRMID },
						// { label: "SF Customer Id", value: commonCRMID },
						{ label: "Custom Region", value: finalAddress.region || "" },
						{ label: "State Code", value: finalAddress.code || "" },
					],
					billing_address: addressPayload,
					shipping_address: addressPayload,
					contact_persons: [
						{
							first_name: customer.name,
							mobile: customer.mobile,
							email: customer.email,
							is_primary_contact: true,
						},
					],
					is_taxable: !isExportCustomer, // No tax for export customers
					language_code: "en",
					ach_supported: false,
					gst_treatment: gstTreatment,
					place_of_contact: isExportCustomer ? "" : (stateDoc?.ZohoStateCode || ""),
					customer_sub_type: "individual",
					opening_balances: [
						{
							opening_balance_amount: "",
							exchange_rate: 1,
							location_id: "", // hardcoded for now; need clarification
						},
					],
					communication_preference: {
						is_email_enabled: true,
						is_sms_enabled: true,
					},
				};

				// Log the payload before making the request
				logger.debug(
					`Zoho customer update payload: ${JSON.stringify({
						contact_id: customer.customer_id,
						payload: payloadData,
					})}`
				);

				// Make the PUT request to update the contact in Zoho
				const response = await axios.put(
					`https://www.zohoapis.in/books/v3/contacts/${customer.customer_id}?organization_id=${config.zoho.organization_id}`,
					payloadData,
					{
						headers: {
							Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
							"Content-Type": "application/json",
						},
					}
				);

				logger.debug(response.data);
				return response.data;
			} catch (error) {
				logger.error("Error updating contact in Zoho:", error);
				throw error;
			}
		},

		getSalesOrderFromZoho: async function ({
			customer_id,
			sort_column = "created_time",
			sort_order = "D",
			salesorder_id,
			SalesRepPID,
		}) {
			try {
				const url = salesorder_id
					? `https://www.zohoapis.in/books/v3/salesorders/${salesorder_id}?organization_id=${config.zoho.organization_id}`
					: SalesRepPID
					? `https://www.zohoapis.in/books/v3/salesorders?organization_id=${config.zoho.organization_id}&cf_salesreppid=${SalesRepPID}&sort_column=${sort_column}&sort_order=${sort_order}`
					: `https://www.zohoapis.in/books/v3/salesorders?organization_id=${config.zoho.organization_id}&customer_id=${customer_id}&sort_column=${sort_column}&sort_order=${sort_order}`;

				const response = await axios({
					method: "GET",
					url,
					headers: {
						Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
						"Content-Type": "application/json",
					},
				});
				return { ok: true, data: response.data };
			} catch (error) {
				logger.error("Error getting sales order from Zoho:", error);
				return { ok: false, message: error.message, data: error.response.data };
			}
		},
	};
};
