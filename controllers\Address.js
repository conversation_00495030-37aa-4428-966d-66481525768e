/**
 * Address Controller
 * This module handles all address-related routes and operations
 */

// Import the logger utility for logging purposes
const { logger } = require("../utils");
const { ObjectId } = require("mongodb");
const axios = require("axios");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Create Address
		 * This route is used to create a new address
		 */
		"POST /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { addressList, customerId } = req.body;

					// Validate input
					if (!Array.isArray(addressList) || addressList.length === 0 || !customerId) {
						return res.status(400).json({
							ok: false,
							message: "Invalid input. Addresses array and customerId are required.",
						});
					}

					// Fetch customer details
					const customer = await Services.Customers.getCustomer({ _id: customerId });

					// Process address types and primary status
					const processedAddresses = addressList.map((address) => ({
						...address,
						primaryAddress: address.primaryAddress || false,
					}));

					// Create address in Salesforce
					const sfRes = await Services.SalesforceHelper.createAddress({
						address: processedAddresses,
						customerCRMID: customer.data[0].customerCRMID,
					});

					// Add Salesforce address ID to our address object
					processedAddresses[0].addressCRMID = sfRes.AddressCRMID;

					// Create address in our database
					const result = await Services.Address.createAddress({
						addressList: processedAddresses,
						customerId,
					});

					if (result.ok) {
						// Send a success response
						res.status(201).json({
							ok: true,
							message: "Addresses created successfully",
							data: result.data,
						});
					} else {
						// Send an error response
						res.status(400).json({ ok: false, message: result.message });
					}
				} catch (error) {
					logger.error("Error creating addresses", error);
					res.status(500).json({ ok: false, message: "An error occurred while creating the addresses" });
				}
			},
		},

		/**
		 * Update Address
		 * This route is used to update an existing address
		 */
		"PUT /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const {
						addressId,
						addressLine1,
						city,
						state,
						country,
						postalCode,
						customerId,
						primaryAddress,
						houseNo,
					} = req.body;

					if (!addressId) {
						return res.status(400).json({ ok: false, message: "Address ID is required" });
					}

					// Create address object with only provided fields
					const addressUpdate = {
						...(addressLine1 && { addressLine1 }),
						...(city && { city }),
						...(state && { state }),
						...(country && { country }),
						...(postalCode && { postalCode }),
						...(customerId && { customerId }),
						...(houseNo && { houseNo }),
						...(primaryAddress !== undefined && { primaryAddress }),
					};

					// Call the service to update the address
					const result = await Services.Address.updateAddress({ addressId, addressList: [addressUpdate] });

					if (result.ok) {
						// Send a success response
						res.json({ ok: true, message: "Address updated successfully" });
					} else {
						// Send an error response
						res.status(400).json({ ok: false, message: result.message });
					}

					if (primaryAddress) {
						// update the address in zoho after updating it in our db.
						const db = await Services.MongoHelper.getDatabaseConnection();
						// 1. get the address
						const address = await db
							.collection("addresses")
							.findOne({ _id: ObjectId.createFromHexString(addressId) });
						// 2. get the customer first
						const customer = await db
							.collection("customers")
							.findOne({ _id: ObjectId.createFromHexString(address.customerId) });

						const zohoResponse = await Services.ZohoHelper.updateContact({ customer, address });
						logger.debug("zoho response after update contact" + JSON.stringify(zohoResponse));
					}
				} catch (error) {
					logger.error("Error updating address", error);
					res.status(500).json({ ok: false, message: "An error occurred while updating the address" });
				}
			},
		},
		/**
		 * Get Addresses by Customer ID
		 * This route is used to fetch addresses for a specific customer
		 */
		"GET /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { customerId } = req.query;

					if (!customerId) {
						return res.status(400).json({ ok: false, message: "Customer ID is required" });
					}

					// Log the address fetch attempt
					// logger.info("Fetching Addresses for Customer", { customerId });

					// Call the service to get addresses for the customer
					const result = await Services.Address.getAddressesByCustomerId(customerId);

					if (result.ok) {
						// Send a success response
						res.json({ ok: true, message: "Addresses fetched successfully", data: result.data });
					} else {
						// Send an error response
						res.status(400).json({ ok: false, message: result.message });
					}
				} catch (error) {
					logger.error("Error fetching addresses", error);
					res.status(500).json({ ok: false, message: "An error occurred while fetching addresses" });
				}
			},
		},

		/**
		 * Delete Address
		 * This route is used to delete an existing address
		 */
		"DELETE /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { addressId } = req.query;

					if (!addressId) {
						return res.status(400).json({ ok: false, message: "Address ID is required" });
					}

					// Call the service to delete the address at our end
					const result = await Services.Address.deleteAddress(addressId);
					//TODO: Delete the address from the sf aswell

					if (result.ok) {
						// Send a success response
						res.json({ ok: true, message: "Address deleted successfully" });
					} else {
						// Send an error response
						res.status(400).json({ ok: false, message: result.message });
					}
				} catch (error) {
					logger.error("Error deleting address", error);
					res.status(500).json({ ok: false, message: "An error occurred while deleting the address" });
				}
			},
		},

		"GET /states": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const states = await Services.Address.getStates();
					res.json({ ok: true, message: "States fetched successfully", data: states });
				} catch (e) {
					logger.error("Error fetching states", e);
					res.status(500).json({ ok: false, message: "An error occurred while fetching states" });
				}
			},
		},

		"GET /state-by-pincode": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { pincode, state, district } = req.query;

					if (!pincode && !state && !district) {
						return res.status(400).json({
							ok: false,
							message: "At least one of pincode, state or district is required",
						});
					}

					const result = await Services.Address.getStateByPinCode({ pincode, state, district });

					if (!result.ok) {
						return res.status(404).json(result);
					}

					res.json({
						ok: true,
						message: "Location data fetched successfully",
						data: result.data,
					});
				} catch (e) {
					logger.error("Error fetching location data", e);
					res.status(500).json({ ok: false, message: "An error occurred while fetching location data" });
				}
			},
		},
	};
};
