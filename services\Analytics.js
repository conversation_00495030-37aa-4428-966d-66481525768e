/**
 * Analytics Service Module
 * This module provides functionality for analytics operations.
 */

const { logger } = require("../utils");
const { ObjectId } = require("mongodb");
const { createExcelFile } = require("../helpers/excelHelper");

module.exports = async function ({ Services }) {
	return {
		/**
		 * Gets the count of non-deleted products
		 * @returns {Number} Count of non-deleted products
		 */
		productsCount: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const count = await db.collection("products").countDocuments({ isDeleted: false });
			return count;
		},

		/**
		 * Gets the count of products with images and the count of products with 1 to 5+ images
		 * @returns {Object} Count of products with images and count of products with 1 to 5+ images
		 */
		productsCountImages: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Count total images across all products
			const totalImagesCount = await db
				.collection("products")
				.aggregate([
					{ $match: { isDeleted: false } },
					{ $project: { imageCount: { $size: { $ifNull: ["$productImages", []] } } } },
					{ $group: { _id: null, totalImages: { $sum: "$imageCount" } } },
				])
				.toArray();

			// Count total products that have at least one image
			const totalProductsWithImagesCount = await db.collection("products").countDocuments({
				isDeleted: false,
				productImages: { $exists: true, $ne: [] }, // Ensures non-empty array
			});

			const counts = {
				totalProductsWithImagesCount,
				totalImagesCount: totalImagesCount.length > 0 ? totalImagesCount[0].totalImages : 0,
			};

			for (let i = 1; i <= 5; i++) {
				counts[`productsWith${i}Images`] = await db.collection("products").countDocuments({
					isDeleted: false,
					productImages: { $size: i }, // Matches exactly i images
				});
			}

			// Count products with 5 or more images
			counts["productsWith5PlusImages"] = await db.collection("products").countDocuments({
				isDeleted: false,
				productImages: { $exists: true, $not: { $size: 0 } }, // Ensure it's not empty
				[`productImages.5`]: { $exists: true }, // At least 6th image exists (zero-based index)
			});

			return counts;
		},

		/**
		 * Gets the count and list of unique product categories
		 * @returns {Object} Count and list of unique product categories
		 */
		categoriesCount: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const categories = await db.collection("products").distinct("category", { isDeleted: false });
			return { count: categories.length, categories };
		},

		/**
		 * Gets the count and list of unique product subcategories
		 * @returns {Object} Count and list of unique product subcategories
		 */
		subCategoriesCount: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const subCategories = await db.collection("products").distinct("subCategory", { isDeleted: false });
			return { count: subCategories.length, subCategories };
		},

		/**
		 * Generates an excel file containing all the products
		 * @returns {Object} Object containing the file URL
		 */
		generateProductsExcel: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const products = await db.collection("products").find({ isDeleted: false }).toArray();

			// Convert products to JSON
			const productsJSON = products.map((product) => ({
				code: product.code,
				BaseUnit: product.BaseUnit,
				Brand: product.Brand,
				Bungalow_Status: product.Bungalow_Status,
				Collection: product.Collection,
				Gross_Weight: product.Gross_Weight,
				HSN: product.HSN,
				MOQ: product.MOQ,
				Net_Weight: product.Net_Weight,
				VendorSKUCode: product.VendorSKUCode,
				category: product.category,
				createdAt: product.createdAt,
				description: product.description,
				isDeleted: product.isDeleted,
				packaging: product.packaging,
				subCategory: product.subCategory,
				updatedAt: product.updatedAt,
				discount: product.discount,
				item_type: product.item_type,
				mrp: product.mrp,
				price: product.price,
				project_id: product.project_id,
				status: product.status,
				stock: product.stock,
				tax_exemption_code: product.tax_exemption_code,
				tax_id: product.tax_id,
				tax_name: product.tax_name,
				template_id: product.template_id,
				unit: product.unit,
				space: product.space,
				item_id: product.item_id,
				Anthology_Space: product.Anthology_Space,
				Brand_name: product.Brand_name,
				Category: product.Category,
				Product_description: product.Product_description,
				Remarks: product.Remarks,
				Sub_Category: product.Sub_Category,
				images:
					product.productImages && Array.isArray(product.productImages)
						? product.productImages.map((image) => image.image).join("|")
						: "",
			}));
			const timestamp = Date.now();
			const fileName = `products_${timestamp}.xlsx`;

			// Create an Excel file with the products data
			const excelFile = await createExcelFile({ data: productsJSON, fileName });

			return excelFile;
		},

		/**
		 * Generates an Excel file containing quotes analytics data
		 * @param {Object} params - Filter parameters
		 * @param {boolean} [params.so] - Filter by SO status (true = with SO, false = without SO)
		 * @param {Date} [params.fromDate] - Start date for filtering
		 * @param {Date} [params.toDate] - End date for filtering
		 * @returns {String} Path to the generated Excel file
		 */
		generateQuotesAnalyticsExcel: async (params = {}) => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const { so, fromDate, toDate } = params;

			// Build date filter if dates are provided
			const dateFilter = fromDate && toDate ? { createdAt: { $gte: fromDate, $lte: toDate } } : {};

			// Build SO filter
			const soFilter =
				so !== undefined
					? so
						? { salesOrderId: { $exists: true } }
						: { salesOrderId: { $exists: false } }
					: {};

			// Get total quotes count
			const totalQuotes = await db.collection("quotes").countDocuments({
				...dateFilter,
			});

			// Get total quotes with SO count
			const quotesWithSOCount = await db.collection("quotes").countDocuments({
				...dateFilter,
				salesOrderId: { $exists: true },
			});

			// Get filtered quotes
			const quotesWithFilter = await db
				.collection("quotes")
				.aggregate([
					{
						$match: {
							...dateFilter,
							...soFilter,
						},
					},
					{
						$lookup: {
							from: "salesOrders",
							localField: "salesOrderId",
							foreignField: "salesorder_id",
							as: "salesOrderDetails",
						},
					},
					{
						$lookup: {
							from: "salesPersons",
							localField: "salesRepPID",
							foreignField: "employeecode",
							as: "salesRepDetails",
						},
					},
					{
						$lookup: {
							from: "customers",
							localField: "customerCRMID",
							foreignField: "customerCRMID",
							as: "customerDetails",
						},
					},
					{
						$unwind: {
							path: "$salesOrderDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$unwind: {
							path: "$salesRepDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$unwind: {
							path: "$customerDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
				])
				.toArray();

			// Transform each quote into a flattened format suitable for Excel
			let excelData = [];
			quotesWithFilter.forEach((quote) => {
				// For each item in the quote's item list, create a row in the Excel
				if (Array.isArray(quote.itemList) && quote.itemList.length > 0) {
					quote.itemList.forEach((item) => {
						excelData.push({
							so_number: quote.salesOrderDetails?.salesorder_number || "N/A",
							salesRepPID: quote.salesRepPID || "N/A",
							salesRepName: quote.salesRepDetails?.name || "N/A",
							customerName: quote.customerDetails?.name || "N/A",
							createdAt: quote.createdAt ? new Date(quote.createdAt).toISOString().split("T")[0] : "N/A",
							sku: item.productName || "N/A",
							quantity: item.quantity || 0,
							category: quote.category || "N/A",
							subCategory: quote.subCategory || item.subCategory || "N/A",
							price: item.price || 0,
							discount: quote.discount_AMOUNT || 0,
							so_status: quote.salesOrderDetails?.status || "N/A",
							quote_no: quote.quoteCRMID || "N/A",
							has_sales_order: quote.salesOrderId ? "Yes" : "No",
						});
					});
				} else {
					// If no items in the itemList, at least add one row with the quote info
					excelData.push({
						so_number: quote.salesOrderDetails?.salesorder_number || "N/A",
						salesRepPID: quote.salesRepPID || "N/A",
						salesRepName: quote.salesRepDetails?.name || "N/A",
						customerName: quote.customerDetails?.name || "N/A",
						createdAt: quote.createdAt ? new Date(quote.createdAt).toISOString().split("T")[0] : "N/A",
						sku: "N/A",
						quantity: 0,
						category: quote.category || "N/A",
						subCategory: quote.subCategory || "N/A",
						price: 0,
						discount: quote.discount_AMOUNT || 0,
						so_status: quote.salesOrderDetails?.status || "N/A",
						quote_no: quote.quoteCRMID || "N/A",
						has_sales_order: quote.salesOrderId ? "Yes" : "No",
					});
				}
			});

			// Prepare summary data
			const summaryData = [
				{
					metric: "Total Quotes",
					value: totalQuotes,
				},
				{
					metric: "Quotes with Sales Orders",
					value: quotesWithSOCount,
				},
				{
					metric: "Conversion Rate",
					value: totalQuotes > 0 ? ((quotesWithSOCount / totalQuotes) * 100).toFixed(2) + "%" : "0%",
				},
				{
					metric: "Filtered Quotes Count",
					value: quotesWithFilter.length,
				},
				{
					metric: "Filter Applied",
					value: so !== undefined ? (so ? "With Sales Orders" : "Without Sales Orders") : "None",
				},
			];

			// Add date filter information to summary if provided
			if (fromDate && toDate) {
				summaryData.push({
					metric: "Date Range",
					value: `${fromDate.toISOString().split("T")[0]} to ${toDate.toISOString().split("T")[0]}`,
				});
			}

			// Create workbook with multiple sheets
			const timestamp = Date.now();
			const fileName = `quotes_analytics_${timestamp}.xlsx`;

			// Create Excel file with both detailed data and summary
			const excelFile = await createExcelFile({
				data: excelData.length > 0 ? excelData : [{ message: "No data found for the applied filters" }],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
				],
			});

			return excelFile;
		},

		/**
		 * Gets activity statistics for a particular user on a specific date range
		 * Optimized for performance
		 *
		 * @param {Object} params - Query parameters
		 * @param {string} params.employeecode - The employee code of the user
		 * @param {Date} params.startDate - Start date for filtering
		 * @param {Date} params.endDate - End date for filtering
		 * @returns {Object} User activity statistics
		 */
		getUserActivityStats: async ({ employeecode, startDate, endDate }) => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Find sales person details (single query)
			const salesPerson = await db
				.collection("salesPersons")
				.findOne({ employeecode }, { projection: { name: 1, email: 1 } });

			// 1. Get Product Events Data - Single optimized aggregation
			const productEvents = await db
				.collection("productEvents")
				.aggregate([
					// Match only relevant events
					{
						$match: {
							employeecode,
							timestamp: { $gte: startDate, $lte: endDate },
						},
					},
					// Add event data
					{
						$project: {
							event: 1,
							skus: 1,
							cartCRMID: 1,
							wishlistCRMID: 1,
							timestamp: 1,
							metadata: 1,
						},
					},
					// Group by event type for stats
					{
						$group: {
							_id: "$event",
							count: { $sum: 1 },
							events: { $push: "$$ROOT" },
						},
					},
				])
				.toArray();

			// 2. Get Cart Details - Find all carts referenced in product events
			const cartCRMIDs = [
				...new Set(
					productEvents
						.flatMap((group) => group.events)
						.filter((event) => event.cartCRMID)
						.map((event) => event.cartCRMID)
				),
			];

			const cartDetails =
				cartCRMIDs.length > 0
					? await db
							.collection("carts")
							.aggregate([
								{
									$match: {
										cartCRMID: { $in: cartCRMIDs },
									},
								},
								{
									$lookup: {
										from: "customers",
										localField: "customerCRMID",
										foreignField: "customerCRMID",
										as: "customerDetails",
									},
								},
								{
									$unwind: {
										path: "$customerDetails",
										preserveNullAndEmptyArrays: true,
									},
								},
								{
									$project: {
										cartCRMID: 1,
										customerCRMID: 1,
										"customerDetails.name": 1,
										"customerDetails.email": 1,
										"customerDetails.mobile": 1,
										"customerDetails.isArchitect": 1,
										"customerDetails.customerCRMID": 1,
										itemList: 1,
									},
								},
							])
							.toArray()
					: [];

			// 3. Get Wishlist Details - Find all wishlists referenced in product events
			const wishlistCRMIDs = [
				...new Set(
					productEvents
						.flatMap((group) => group.events)
						.filter((event) => event.wishlistCRMID)
						.map((event) => event.wishlistCRMID)
				),
			];

			const wishlistDetails =
				wishlistCRMIDs.length > 0
					? await db
							.collection("wishlists")
							.aggregate([
								{
									$match: {
										wishlistCRMID: { $in: wishlistCRMIDs },
									},
								},
								{
									$lookup: {
										from: "customers",
										localField: "customerCRMID",
										foreignField: "customerCRMID",
										as: "customerDetails",
									},
								},
								{
									$unwind: {
										path: "$customerDetails",
										preserveNullAndEmptyArrays: true,
									},
								},
								{
									$project: {
										wishlistCRMID: 1,
										customerCRMID: 1,
										"customerDetails.name": 1,
										"customerDetails.email": 1,
										"customerDetails.mobile": 1,
										"customerDetails.isArchitect": 1,
										"customerDetails.customerCRMID": 1,
										itemList: 1,
									},
								},
							])
							.toArray()
					: [];

			// 4. Get Product Details - Find all unique SKUs
			const allSkus = [
				...new Set(productEvents.flatMap((group) => group.events).flatMap((event) => event.skus || [])),
			];

			const productDetails =
				allSkus.length > 0
					? await db
							.collection("products")
							.find(
								{ code: { $in: allSkus } },
								{
									projection: {
										code: 1,
										name: 1,
										description: 1,
										category: 1,
										subCategory: 1,
									},
								}
							)
							.toArray()
					: [];

			// Create lookup maps for faster access
			const productMap = productDetails.reduce((map, product) => {
				map[product.code] = product;
				return map;
			}, {});

			const cartMap = cartDetails.reduce((map, cart) => {
				map[cart.cartCRMID] = cart;
				return map;
			}, {});

			const wishlistMap = wishlistDetails.reduce((map, wishlist) => {
				map[wishlist.wishlistCRMID] = wishlist;
				return map;
			}, {});

			// 5. Process events and generate detailed data
			// Process cart-related activity stats
			const cartActivityEvents = productEvents
				.filter((group) => ["addToCart", "moveToCart"].includes(group._id))
				.flatMap((group) => group.events);

			const wishlistActivityEvents = productEvents
				.filter((group) => ["addToWishlist"].includes(group._id))
				.flatMap((group) => group.events);

			// Format individual level stats
			const individualStats = [];

			// Process cart activities
			cartActivityEvents.forEach((event) => {
				const cart = cartMap[event.cartCRMID];
				if (!cart) return;

				const customer = cart.customerDetails || {};

				(event.skus || []).forEach((sku) => {
					const product = productMap[sku] || {};

					individualStats.push({
						userId: employeecode,
						userName: salesPerson?.name || "Unknown",
						customerType: customer.isArchitect ? "Architect" : "Regular",
						customerId: customer.customerCRMID || "N/A",
						customerName: customer.name || "N/A",
						customerEmail: customer.email || "N/A",
						customerMobile: customer.mobile || "N/A",
						sku,
						productName: product.name || product.description || "N/A",
						category: product.category || "N/A",
						subCategory: product.subCategory || "N/A",
						quantity: event.metadata?.quantity || 1, // Get quantity from metadata if available
						actionType: "Cart",
						event: event.event,
						cartCRMID: event.cartCRMID,
						timestamp: event.timestamp,
					});
				});
			});

			// Process wishlist activities
			wishlistActivityEvents.forEach((event) => {
				const wishlist = wishlistMap[event.wishlistCRMID];
				if (!wishlist) return;

				const customer = wishlist.customerDetails || {};

				(event.skus || []).forEach((sku) => {
					const product = productMap[sku] || {};

					individualStats.push({
						userId: employeecode,
						userName: salesPerson?.name || "Unknown",
						customerType: customer.isArchitect ? "Architect" : "Regular",
						customerId: customer.customerCRMID || "N/A",
						customerName: customer.name || "N/A",
						customerEmail: customer.email || "N/A",
						customerMobile: customer.mobile || "N/A",
						sku,
						productName: product.name || product.description || "N/A",
						category: product.category || "N/A",
						subCategory: product.subCategory || "N/A",
						quantity: event.metadata?.quantity || 1,
						actionType: "Wishlist",
						event: event.event,
						wishlistCRMID: event.wishlistCRMID,
						timestamp: event.timestamp,
					});
				});
			});

			// Count distinct customers
			const regularCustomerIds = new Set();
			const architectCustomerIds = new Set();

			cartDetails.forEach((cart) => {
				if (cart.customerDetails) {
					if (cart.customerDetails.isArchitect) {
						architectCustomerIds.add(cart.customerDetails.customerCRMID);
					} else {
						regularCustomerIds.add(cart.customerDetails.customerCRMID);
					}
				}
			});

			wishlistDetails.forEach((wishlist) => {
				if (wishlist.customerDetails) {
					if (wishlist.customerDetails.isArchitect) {
						architectCustomerIds.add(wishlist.customerDetails.customerCRMID);
					} else {
						regularCustomerIds.add(wishlist.customerDetails.customerCRMID);
					}
				}
			});

			// Count product events by type
			const eventCounts = productEvents.reduce((counts, group) => {
				counts[group._id] = group.count;
				return counts;
			}, {});

			// Count all skus added
			const skusCount = {
				addToCart: productEvents
					.filter((group) => group._id === "addToCart")
					.flatMap((group) => group.events)
					.flatMap((event) => event.skus || []).length,
				addToWishlist: productEvents
					.filter((group) => group._id === "addToWishlist")
					.flatMap((group) => group.events)
					.flatMap((event) => event.skus || []).length,
			};

			return {
				userDetails: {
					employeecode,
					name: salesPerson?.name || "Unknown",
					email: salesPerson?.email || "Unknown",
					dateRange: `${startDate.toISOString().split("T")[0]} to ${endDate.toISOString().split("T")[0]}`,
				},
				summary: {
					totalCartActivities: cartActivityEvents.length,
					totalWishlistActivities: wishlistActivityEvents.length,
					regularCustomersCount: regularCustomerIds.size,
					architectCustomersCount: architectCustomerIds.size,
					skusAddedToCartCount: skusCount.addToCart,
					skusAddedToWishlistCount: skusCount.addToWishlist,
					eventCounts,
				},
				details: {
					individualStats,
				},
			};
		},

		/**
		 * Generates an Excel file with user activity data
		 * @param {Object} params - Query parameters
		 * @param {string} params.employeecode - The employee code of the user
		 * @param {Date} params.startDate - Start date for filtering
		 * @param {Date} params.endDate - End date for filtering
		 * @returns {string} Path to the generated Excel file
		 */
		generateUserActivityExcel: async ({ employeecode, startDate, endDate }) => {
			// Get user activity stats
			const stats = await Services.Analytics.getUserActivityStats({ employeecode, startDate, endDate });

			// Create formatted data for Excel
			const userActivityData = [];

			// Process individual stats directly
			if (stats.details.individualStats && stats.details.individualStats.length > 0) {
				stats.details.individualStats.forEach((activity) => {
					userActivityData.push({
						"User ID": activity.userId,
						"User Name": activity.userName,
						"Activity Type": activity.actionType,
						Event: activity.event || "N/A",
						"Customer Type": activity.customerType,
						"Customer ID": activity.customerId,
						"Customer Name": activity.customerName,
						"Customer Email": activity.customerEmail,
						"Customer Mobile": activity.customerMobile,
						SKU: activity.sku || "N/A",
						"Product Name": activity.productName || "N/A",
						Category: activity.category || "N/A",
						SubCategory: activity.subCategory || "N/A",
						Quantity: activity.quantity || "N/A",
						"Cart ID": activity.cartCRMID || "N/A",
						"Wishlist ID": activity.wishlistCRMID || "N/A",
						"Date Time": activity.timestamp ? new Date(activity.timestamp).toLocaleString() : "N/A",
					});
				});
			}

			// Create summary data
			const summaryData = [
				{ metric: "Employee Code", value: stats.userDetails.employeecode },
				{ metric: "Employee Name", value: stats.userDetails.name },
				{ metric: "Employee Email", value: stats.userDetails.email },
				{ metric: "Date Range", value: stats.userDetails.dateRange },
				{ metric: "Total Activities", value: userActivityData.length },
				{ metric: "Cart Activities", value: stats.summary.totalCartActivities },
				{ metric: "Wishlist Activities", value: stats.summary.totalWishlistActivities },
				{ metric: "Regular Customers", value: stats.summary.regularCustomersCount },
				{ metric: "Architect Customers", value: stats.summary.architectCustomersCount },
				{ metric: "SKUs Added To Cart", value: stats.summary.skusAddedToCartCount },
				{ metric: "SKUs Added To Wishlist", value: stats.summary.skusAddedToWishlistCount },
			];

			// Add event counts breakdown
			if (stats.summary.eventCounts) {
				Object.entries(stats.summary.eventCounts).forEach(([event, count]) => {
					summaryData.push({ metric: `Event: ${event}`, value: count });
				});
			}

			// If no activities found, provide a more detailed message
			const noDataMessage = {
				message: "No activities found for the specified filters",
				"Employee Code": stats.userDetails.employeecode,
				"Employee Name": stats.userDetails.name,
				"Date Range": stats.userDetails.dateRange,
				Note: "Please verify the employee code and date range",
			};

			// Create Excel file
			const timestamp = Date.now();
			const fileName = `user_activity_${employeecode}_${timestamp}.xlsx`;

			const excelFile = await createExcelFile({
				data: userActivityData.length > 0 ? userActivityData : [noDataMessage],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
				],
			});

			return excelFile;
		},

		/**
		 * Gets contribution statistics for a specific sales order
		 * Optimized for performance
		 *
		 * @param {Object} params - Query parameters
		 * @param {string} params.salesorder_id - The sales order ID
		 * @returns {Object} Sales order contribution statistics
		 */
		getSalesOrderContributionStats: async ({ salesorder_id }) => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Get the sales order details - use projection to retrieve only needed fields
			const salesOrder = await db
				.collection("salesOrders")
				.findOne(
					{ salesorder_id },
					{ projection: { salesorder_number: 1, created_time: 1, reference_number: 1, line_items: 1 } }
				);

			if (!salesOrder) {
				return { error: "Sales order not found" };
			}

			// Extract reference numbers (quotes) and line items in single operation
			const quoteIds = salesOrder.reference_number ? salesOrder.reference_number.split(",") : [];
			const soSkus = (salesOrder.line_items || []).map((item) => item.sku);

			if (soSkus.length === 0) {
				return { error: "No SKUs found in this sales order" };
			}

			// Get quotes and sales rep details in one aggregation
			const quotes =
				quoteIds.length > 0
					? await db
							.collection("quotes")
							.aggregate([
								{
									$match: {
										quoteCRMID: { $in: quoteIds },
									},
								},
								{
									$lookup: {
										from: "salesPersons",
										localField: "salesRepPID",
										foreignField: "employeecode",
										as: "salesRepDetails",
									},
								},
								{
									$lookup: {
										from: "customers",
										localField: "customerCRMID",
										foreignField: "customerCRMID",
										as: "customerDetails",
									},
								},
								{
									$unwind: {
										path: "$salesRepDetails",
										preserveNullAndEmptyArrays: true,
									},
								},
								{
									$unwind: {
										path: "$customerDetails",
										preserveNullAndEmptyArrays: true,
									},
								},
								{
									$project: {
										quoteCRMID: 1,
										salesRepPID: 1,
										customerCRMID: 1,
										"salesRepDetails.name": 1,
										"customerDetails.name": 1,
										"customerDetails.email": 1,
										"customerDetails.mobile": 1,
										"customerDetails.isArchitect": 1,
										"customerDetails.customerCRMID": 1,
									},
								},
							])
							.toArray()
					: [];

			// Find who added these SKUs to the cart - optimize the query
			const cartEvents = await db
				.collection("productEvents")
				.aggregate([
					{
						$match: {
							skus: { $in: soSkus },
							event: { $in: ["addToCart", "moveToCart"] },
						},
					},
					// Sort first by timestamp to ensure we get the earliest events
					{
						$sort: {
							timestamp: -1,
						},
					},
					// Lookup sales rep details
					{
						$lookup: {
							from: "salesPersons",
							localField: "employeecode",
							foreignField: "employeecode",
							as: "salesRepDetails",
						},
					},
					{
						$unwind: {
							path: "$salesRepDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					// Unwind skus array to process each SKU individually
					{
						$unwind: {
							path: "$skus",
						},
					},
					// Filter to only include SKUs that are part of this SO
					{
						$match: {
							skus: { $in: soSkus },
						},
					},
					// Group by SKU to get the first person who added each
					{
						$group: {
							_id: "$skus",
							addedBy: { $first: "$employeecode" },
							salesRepName: { $first: "$salesRepDetails.name" },
							timestamp: { $first: "$timestamp" },
						},
					},
				])
				.toArray();

			// Extract SO creator info
			const soCreator = quotes.length > 0 ? quotes[0].salesRepPID : null;
			const soCreatorName =
				quotes.length > 0 && quotes[0].salesRepDetails ? quotes[0].salesRepDetails.name : "Unknown";

			// Process the cart events to identify creator vs. non-creator contributions
			const skusAddedByCreator = cartEvents
				.filter((event) => event.addedBy === soCreator)

			const skusAddedByOthers = cartEvents
				.filter((event) => event.addedBy !== soCreator && event.addedBy)
				.map((event) => ({
					sku: event._id,
					addedBy: event.addedBy,
					salesRepName: event.salesRepName,
					timestamp: event.timestamp,
				}));

			// Get customer information
			const customer =
				quotes.length > 0 && quotes[0].customerDetails
					? {
							customerCRMID: quotes[0].customerCRMID,
							name: quotes[0].customerDetails.name,
							email: quotes[0].customerDetails.email,
							mobile: quotes[0].customerDetails.mobile,
							isArchitect: quotes[0].customerDetails.isArchitect,
					  }
					: null;

			return {
				salesOrderDetails: {
					salesOrderId: salesorder_id,
					salesOrderNumber: salesOrder.salesorder_number,
					createdAt: salesOrder.created_time,
					soCreator,
					soCreatorName,
					customer,
				},
				contributionStats: {
					totalSkus: soSkus.length,
					skusAddedByCreator: skusAddedByCreator.length,
					skusAddedByOthers: skusAddedByOthers.length,
					notTracked: soSkus.length - (skusAddedByCreator.length + skusAddedByOthers.length),
				},
				items: {
					skusAddedByCreator,
					skusAddedByOthers,
					allSkus: soSkus,
				},
			};
		},

		/**
		 * Generates an Excel file with sales order contribution data
		 * @param {Object} params - Query parameters
		 * @param {Date} params.startDate - Start date for filtering
		 * @param {Date} params.endDate - End date for filtering
		 * @returns {string} Path to the generated Excel file
		 */
		generateSalesOrderContributionExcel: async ({ startDate, endDate }) => {
			const db = Services.MongoHelper.getDatabaseConnection();

			logger.info(
				`Generating sales order contribution excel for period: ${startDate.toISOString()} to ${endDate.toISOString()}`
			);

			// First, find quotes created in the date range
			// Quotes have proper Date objects for the createdAt field
			const quotes = await db
				.collection("quotes")
				.find(
					{
						createdAt: { $gte: startDate, $lte: endDate },
						salesOrderId: { $exists: true, $ne: null }, // Only get quotes linked to sales orders
					},
					{
						projection: {
							salesOrderId: 1,
							customerCRMID: 1,
							salesRepPID: 1,
							quoteCRMID: 1,
						},
					}
				)
				.toArray();

			// Extract unique sales order IDs
			const salesOrderIds = [...new Set(quotes.map((quote) => quote.salesOrderId))];

			logger.info(`Found ${salesOrderIds.length} unique sales orders from ${quotes.length} quotes`);

			if (salesOrderIds.length === 0) {
				// Early return if no sales orders found
				const timestamp = Date.now();
				const fileName = `sales_order_contributions_${timestamp}.xlsx`;

				const excelFile = await createExcelFile({
					data: [{ message: "No sales orders found for the specified date range" }],
					fileName,
					additionalSheets: [
						{
							name: "Summary",
							data: [
								{
									metric: "Date Range",
									value: `${startDate.toISOString().split("T")[0]} to ${
										endDate.toISOString().split("T")[0]
									}`,
								},
								{ metric: "Total Sales Orders", value: 0 },
							],
						},
					],
				});

				return excelFile;
			}

			// Create a map of salesOrderId to quote details for quick lookup
			const salesOrderToQuoteMap = {};
			quotes.forEach((quote) => {
				if (!salesOrderToQuoteMap[quote.salesOrderId]) {
					salesOrderToQuoteMap[quote.salesOrderId] = {
						customerCRMID: quote.customerCRMID,
						salesRepPID: quote.salesRepPID,
						quoteCRMID: quote.quoteCRMID,
					};
				}
			});

			// Fetch sales orders using the IDs we found
			const salesOrders = await db
				.collection("salesOrders")
				.find(
					{
						salesorder_id: { $in: salesOrderIds },
					},
					{
						projection: {
							salesorder_id: 1,
							salesorder_number: 1,
							line_items: 1,
							reference_number: 1,
							created_time: 1,
							customer_id: 1,
						},
					}
				)
				.limit(50) // Add a safety limit
				.toArray();

			// Get all unique SKUs to fetch product details
			const allSkus = new Set();
			const soSkusMap = {}; // Map of salesorder_id to its SKUs
			salesOrders.forEach((order) => {
				const orderSkus = [];
				if (order.line_items && Array.isArray(order.line_items)) {
					order.line_items.forEach((item) => {
						if (item.sku) {
							allSkus.add(item.sku);
							orderSkus.push(item.sku);
						}
					});
				}
				soSkusMap[order.salesorder_id] = orderSkus;
			});

			// Fetch product details for all SKUs at once
			const productDetails =
				allSkus.size > 0
					? await db
							.collection("products")
							.find(
								{ code: { $in: [...allSkus] } },
								{
									projection: {
										code: 1,
										name: 1,
										description: 1,
										category: 1,
										subCategory: 1,
									},
								}
							)
							.toArray()
					: [];

			// Create product lookup map for quick access
			const productMap = {};
			productDetails.forEach((product) => {
				productMap[product.code] = product;
			});

			// Fetch all sales rep details at once
			const allSalesRepIds = new Set();
			Object.values(salesOrderToQuoteMap).forEach((quote) => {
				if (quote.salesRepPID) allSalesRepIds.add(quote.salesRepPID);
			});

			const salesReps =
				allSalesRepIds.size > 0
					? await db
							.collection("salesPersons")
							.find(
								{ employeecode: { $in: [...allSalesRepIds] } },
								{ projection: { employeecode: 1, name: 1 } }
							)
							.toArray()
					: [];

			const salesRepMap = {};
			salesReps.forEach((rep) => {
				salesRepMap[rep.employeecode] = rep;
			});

			// Fetch all customer details at once
			const allCustomerIds = new Set();
			Object.values(salesOrderToQuoteMap).forEach((quote) => {
				if (quote.customerCRMID) allCustomerIds.add(quote.customerCRMID);
			});

			const customers =
				allCustomerIds.size > 0
					? await db
							.collection("customers")
							.find(
								{ customerCRMID: { $in: [...allCustomerIds] } },
								{
									projection: {
										customerCRMID: 1,
										name: 1,
										email: 1,
										mobile: 1,
										isArchitect: 1,
									},
								}
							)
							.toArray()
					: [];

			const customerMap = {};
			customers.forEach((customer) => {
				customerMap[customer.customerCRMID] = customer;
			});

			// Find all SKU cart addition events for all these SKUs at once
			const skusArray = [...allSkus];
			const cartEvents =
				skusArray.length > 0
					? await db
							.collection("productEvents")
							.aggregate([
								{
									$match: {
										skus: { $in: skusArray },
										event: { $in: ["addToCart", "moveToCart"] },
									},
								},
								// Sort first by timestamp to ensure we get the earliest events for each SKU
								{
									$sort: {
										timestamp: -1,
									},
								},
								// Lookup sales rep details
								{
									$lookup: {
										from: "salesPersons",
										localField: "employeecode",
										foreignField: "employeecode",
										as: "salesRepDetails",
									},
								},
								{
									$unwind: {
										path: "$salesRepDetails",
										preserveNullAndEmptyArrays: true,
									},
								},
								// Unwind skus array to process each SKU individually
								{
									$unwind: {
										path: "$skus",
									},
								},
								// Filter to only include SKUs that are part of our analysis
								{
									$match: {
										skus: { $in: skusArray },
									},
								},
								// Group by SKU to get the first person who added each
								{
									$group: {
										_id: "$skus",
										addedBy: { $first: "$employeecode" },
										salesRepName: { $first: "$salesRepDetails.name" },
										timestamp: { $first: "$timestamp" },
									},
								},
							])
							.toArray()
					: [];

			// Create lookup map for cart events
			const cartEventsMap = {};
			cartEvents.forEach((event) => {
				cartEventsMap[event._id] = {
					addedBy: event.addedBy,
					salesRepName: event.salesRepName,
					timestamp: event.timestamp,
				};
			});

			const contributionData = [];
			let soWithMultipleContributors = 0;

			// Process each sales order
			for (const salesOrder of salesOrders) {
				try {
					// Get the sales order's SKUs
					const skus = soSkusMap[salesOrder.salesorder_id] || [];

					if (skus.length === 0) {
						logger.warn(`Skipping sales order ${salesOrder.salesorder_id}: No SKUs found`);
						continue;
					}

					// Get quote info for this sales order
					const quoteInfo = salesOrderToQuoteMap[salesOrder.salesorder_id] || {};
					const soCreator = quoteInfo.salesRepPID;
					const soCreatorDetails = salesRepMap[soCreator] || {};
					const customerDetails = customerMap[quoteInfo.customerCRMID] || {};

					// Track if this SO has multiple contributors
					let hasMultipleContributors = false;

					// Process each sku
					for (const sku of skus) {
						const cartEvent = cartEventsMap[sku];
						let addedBy, salesRepName, timestamp;

						if (cartEvent) {
							addedBy = cartEvent.addedBy;
							salesRepName = cartEvent.salesRepName;
							timestamp = cartEvent.timestamp;
						}

						const addedByCreator = addedBy === soCreator;

						// Check if this SO has contributors other than the creator
						if (addedBy && addedBy !== soCreator) {
							hasMultipleContributors = true;
						}

						// Get product details
						const product = productMap[sku] || {};

						contributionData.push({
							"SO Number": salesOrder.salesorder_number || "N/A",
							"Customer ID": customerDetails.customerCRMID || "N/A",
							"Customer Name": customerDetails.name || "N/A",
							"Customer Email": customerDetails.email || "N/A",
							"Customer Mobile": customerDetails.mobile || "N/A",
							"Customer Type": customerDetails.isArchitect ? "Architect" : "Regular",
							"SO Created By": soCreatorDetails.name || "Unknown",
							"SO Created Date": salesOrder.created_time
								? new Date(salesOrder.created_time).toISOString().split("T")[0]
								: "N/A",
							SKU: sku || "N/A",
							"Product Name": product.name || product.description || "N/A",
							Category: product.category || "N/A",
							SubCategory: product.subCategory || "N/A",
							"Added By Creator": addedByCreator ? "Yes" : "No",
							"Added By": addedByCreator
								? soCreatorDetails.name || "Unknown"
								: cartEvent
								? salesRepName || "Unknown"
								: "Unknown",
							"User ID": addedByCreator
								? soCreator || "Unknown"
								: cartEvent
								? addedBy || "Unknown"
								: "Unknown",
							"Tracking Status": cartEvent ? "Tracked" : "Not Tracked",
							"Added On": timestamp ? new Date(timestamp).toISOString().split("T")[0] : "N/A",
						});
					}

					if (hasMultipleContributors) {
						soWithMultipleContributors++;
					}
				} catch (error) {
					logger.error(`Error processing sales order ${salesOrder.salesorder_id}: ${error.message}`);
					continue;
				}
			}

			// Create summary data
			const soCount = salesOrders.length;
			const processedCount =
				contributionData.length > 0
					? [...new Set(contributionData.map((item) => item["SO Number"]))].length
					: 0;

			const summaryData = [
				{
					metric: "Date Range",
					value: `${startDate.toISOString().split("T")[0]} to ${endDate.toISOString().split("T")[0]}`,
				},
				{ metric: "Total Sales Orders Found", value: salesOrderIds.length },
				{ metric: "Sales Orders Processed (Limited to 50)", value: soCount },
				{ metric: "Sales Orders Successfully Analyzed", value: processedCount },
				{ metric: "Sales Orders with Multiple Contributors", value: soWithMultipleContributors },
				{
					metric: "Percentage with Multiple Contributors",
					value:
						processedCount > 0
							? `${((soWithMultipleContributors / processedCount) * 100).toFixed(2)}%`
							: "0%",
				},
				{ metric: "Total SKUs Analyzed", value: contributionData.length },
				{
					metric: "SKUs Added by Creator",
					value: contributionData.filter((item) => item["Added By Creator"] === "Yes").length,
				},
				{
					metric: "SKUs Added by Others",
					value: contributionData.filter(
						(item) => item["Added By Creator"] === "No" && item["Tracking Status"] === "Tracked"
					).length,
				},
				{
					metric: "SKUs Not Tracked",
					value: contributionData.filter((item) => item["Tracking Status"] === "Not Tracked").length,
				},
			];

			// Create Excel file
			const timestamp = Date.now();
			const fileName = `sales_order_contributions_${timestamp}.xlsx`;

			const excelFile = await createExcelFile({
				data:
					contributionData.length > 0
						? contributionData
						: [{ message: "No data found for the applied filters" }],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
				],
			});

			return excelFile;
		},

		/**
		 * Fetches the "Total Carts Open" report.
		 * @param {Object} params - Query parameters.
		 * @param {Date} [params.fromDate] - Start date for filtering.
		 * @param {Date} [params.toDate] - End date for filtering.
		 * @param {String} [params.employeecode] - Sales representative's employee code.
		 * @param {String} [params.customerCRMID] - Customer's CRM ID.
		 * @returns {Array} List of carts with details.
		 */
		getTotalCartsOpenReport: async ({ fromDate, toDate, employeecode, customerCRMID }) => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Build match condition based on provided parameters
			const matchConditions = {};

			// Add date range filter if provided
			if (fromDate && toDate) {
				matchConditions.createdAt = { $gte: fromDate, $lte: toDate };
			}

			// Add employee code filter if provided
			if (employeecode) {
				matchConditions.salesRepPID = employeecode;
			}

			// Add customer filter if provided
			if (customerCRMID) {
				matchConditions.customerCRMID = customerCRMID;
			}

			// Validate that at least one filter criteria is provided
			if (!fromDate && !toDate && !employeecode && !customerCRMID) {
				throw new Error("At least one filter criteria is required: date range, employeecode, or customerCRMID");
			}

			// Fetch carts based on filters
			const carts = await db
				.collection("carts")
				.aggregate([
					{
						$match: matchConditions,
					},
					{
						$lookup: {
							from: "customers",
							localField: "customerCRMID",
							foreignField: "customerCRMID",
							as: "customerDetails",
						},
					},
					{
						$unwind: {
							path: "$customerDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$lookup: {
							from: "salesPersons",
							localField: "salesRepPID",
							foreignField: "employeecode",
							as: "salesRepDetails",
						},
					},
					{
						$unwind: {
							path: "$salesRepDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$lookup: {
							from: "productEvents",
							localField: "cartCRMID",
							foreignField: "cartCRMID",
							as: "cartEvents",
						},
					},
					{
						$project: {
							cartCRMID: 1,
							customerCRMID: 1,
							salesRepPID: 1,
							"customerDetails.name": 1,
							"customerDetails.email": 1,
							"customerDetails.mobile": 1,
							"salesRepDetails.name": 1,
							"salesRepDetails.email": 1,
							itemList: 1,
							createdAt: 1,
							updatedAt: 1,
							cartEvents: {
								$filter: {
									input: "$cartEvents",
									as: "event",
									cond: {
										$or: [
											{ $eq: ["$$event.event", "addToCart"] },
											{ $eq: ["$$event.event", "removeFromCart"] },
											{ $eq: ["$$event.event", "moveToWishlist"] },
										],
									},
								},
							},
						},
					},
				])
				.toArray();

			// Process each cart to include detailed information
			const report = carts.map((cart) => {
				const skusAdded = [];
				const skusDeleted = [];
				const skusMovedToWishlist = [];

				// Process cart events
				cart.cartEvents.forEach((event) => {
					if (event.event === "addToCart") {
						skusAdded.push(...event.skus);
					} else if (event.event === "removeFromCart") {
						skusDeleted.push(...event.skus);
					} else if (event.event === "moveToWishlist") {
						skusMovedToWishlist.push(...event.skus);
					}
				});

				return {
					cartId: cart.cartCRMID,
					customerId: cart.customerCRMID,
					customerName: cart.customerDetails?.name || "N/A",
					customerEmail: cart.customerDetails?.email || "N/A",
					customerMobile: cart.customerDetails?.mobile || "N/A",
					salesRepId: cart.salesRepPID,
					salesRepName: cart.salesRepDetails?.name || "N/A",
					salesRepEmail: cart.salesRepDetails?.email || "N/A",
					skusAdded,
					quantity: cart.itemList.reduce((sum, item) => sum + (item.quantity || 0), 0),
					dateCreated: cart.createdAt,
					lastUpdated: cart.updatedAt,
					skusDeleted,
					skusMovedToWishlist,
				};
			});

			return report;
		},

		/**
		 * Fetches the "Total Wishlists Created" report.
		 * @param {Object} params - Query parameters.
		 * @param {Date} [params.fromDate] - Start date for filtering.
		 * @param {Date} [params.toDate] - End date for filtering.
		 * @param {String} [params.employeecode] - Sales representative's employee code.
		 * @param {String} [params.customerCRMID] - Customer's CRM ID.
		 * @returns {Array} List of wishlists with details.
		 */
		getTotalWishlistsCreatedReport: async ({ fromDate, toDate, employeecode, customerCRMID }) => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Build match condition based on provided parameters
			const matchConditions = {};

			// Add date range filter if provided
			if (fromDate && toDate) {
				matchConditions.createdAt = { $gte: fromDate, $lte: toDate };
			}

			// Add employee code filter if provided
			if (employeecode) {
				matchConditions.salesRepPID = employeecode;
			}

			// Add customer filter if provided
			if (customerCRMID) {
				matchConditions.customerCRMID = customerCRMID;
			}

			// Validate that at least one filter criteria is provided
			if (!fromDate && !toDate && !employeecode && !customerCRMID) {
				throw new Error("At least one filter criteria is required: date range, employeecode, or customerCRMID");
			}

			// Fetch wishlists based on filters
			const wishlists = await db
				.collection("wishlists")
				.aggregate([
					{
						$match: matchConditions,
					},
					{
						$lookup: {
							from: "customers",
							localField: "customerCRMID",
							foreignField: "customerCRMID",
							as: "customerDetails",
						},
					},
					{
						$unwind: {
							path: "$customerDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$lookup: {
							from: "salesPersons",
							localField: "salesRepPID",
							foreignField: "employeecode",
							as: "salesRepDetails",
						},
					},
					{
						$unwind: {
							path: "$salesRepDetails",
							preserveNullAndEmptyArrays: true,
						},
					},
					{
						$lookup: {
							from: "productEvents",
							localField: "wishlistCRMID",
							foreignField: "wishlistCRMID",
							as: "wishlistEvents",
						},
					},
					{
						$project: {
							wishlistCRMID: 1,
							wishlistName: 1,
							customerCRMID: 1,
							salesRepPID: 1,
							"customerDetails.name": 1,
							"customerDetails.email": 1,
							"customerDetails.mobile": 1,
							"salesRepDetails.name": 1,
							"salesRepDetails.email": 1,
							itemList: 1,
							createdAt: 1,
							updatedAt: 1,
							wishlistEvents: {
								$filter: {
									input: "$wishlistEvents",
									as: "event",
									cond: {
										$or: [
											{ $eq: ["$$event.event", "addToWishlist"] },
											{ $eq: ["$$event.event", "removeFromWishlist"] },
											{ $eq: ["$$event.event", "moveToCart"] },
										],
									},
								},
							},
						},
					},
				])
				.toArray();

			// Process each wishlist to include detailed information
			const report = wishlists.map((wishlist) => {
				const skusAdded = [];
				const skusDeleted = [];
				const skusMovedToCart = [];

				// Process wishlist events
				wishlist.wishlistEvents.forEach((event) => {
					if (event.event === "addToWishlist") {
						skusAdded.push(...(event.skus || []));
					} else if (event.event === "removeFromWishlist") {
						skusDeleted.push(...(event.skus || []));
					} else if (event.event === "moveToCart") {
						skusMovedToCart.push(...(event.skus || []));
					}
				});

				return {
					wishlistId: wishlist.wishlistCRMID,
					wishlistName: wishlist.wishlistName || "Unnamed Wishlist",
					customerId: wishlist.customerCRMID,
					customerName: wishlist.customerDetails?.name || "N/A",
					customerEmail: wishlist.customerDetails?.email || "N/A",
					customerMobile: wishlist.customerDetails?.mobile || "N/A",
					salesRepId: wishlist.salesRepPID,
					salesRepName: wishlist.salesRepDetails?.name || "N/A",
					salesRepEmail: wishlist.salesRepDetails?.email || "N/A",
					dateCreated: wishlist.createdAt,
					lastUpdated: wishlist.updatedAt,
					skusAdded,
					skusDeleted,
					skusMovedToCart,
					totalItems: (wishlist.itemList || []).length,
					itemQuantity: (wishlist.itemList || []).reduce((sum, item) => sum + (item.quantity || 0), 0),
				};
			});

			return report;
		},

		/**
		 * Generates an Excel file with Total Wishlists Created report data
		 * @param {Object} params - Query parameters
		 * @param {Date} params.fromDate - Start date for filtering
		 * @param {Date} params.toDate - End date for filtering
		 * @param {String} [params.employeecode] - Sales representative's employee code
		 * @param {String} [params.customerCRMID] - Customer's CRM ID
		 * @returns {string} Path to the generated Excel file
		 */
		generateTotalWishlistsCreatedExcel: async ({ fromDate, toDate, employeecode, customerCRMID }) => {
			// Get wishlist report data
			const report = await Services.Analytics.getTotalWishlistsCreatedReport({
				fromDate,
				toDate,
				employeecode,
				customerCRMID,
			});

			// Format data for Excel
			const excelData = report.map((wishlist) => ({
				"Wishlist ID": wishlist.wishlistId || "N/A",
				"Wishlist Name": wishlist.wishlistName || "N/A",
				"Customer ID": wishlist.customerId || "N/A",
				"Customer Name": wishlist.customerName || "N/A",
				"Customer Email": wishlist.customerEmail || "N/A",
				"Customer Mobile": wishlist.customerMobile || "N/A",
				"Sales Rep ID": wishlist.salesRepId || "N/A",
				"Sales Rep Name": wishlist.salesRepName || "N/A",
				"Date Created": wishlist.dateCreated ? wishlist.dateCreated.toISOString().split("T")[0] : "N/A",
				"Last Updated": wishlist.lastUpdated ? wishlist.lastUpdated.toISOString().split("T")[0] : "N/A",
				"Total SKUs Added": wishlist.skusAdded.length,
				"Total SKUs Deleted": wishlist.skusDeleted.length,
				"Total SKUs Moved to Cart": wishlist.skusMovedToCart.length,
				"Current Items Count": wishlist.totalItems,
				"Current Items Quantity": wishlist.itemQuantity,
				"SKUs Added": wishlist.skusAdded.join(", "),
				"SKUs Deleted": wishlist.skusDeleted.join(", "),
				"SKUs Moved to Cart": wishlist.skusMovedToCart.join(", "),
			}));

			logger.debug(typeof fromDate)
			// Create summary data
			const summaryData = [
				{
					metric: "Date Range",
					value: `${fromDate instanceof Date ? fromDate.toISOString().split("T")[0] : fromDate} to ${toDate instanceof Date ? toDate.toISOString().split("T")[0] : toDate}`,
				},
				{ metric: "Total Wishlists", value: report.length },
				{
					metric: "Total SKUs Added",
					value: report.reduce((sum, wishlist) => sum + wishlist.skusAdded.length, 0),
				},
				{
					metric: "Total SKUs Deleted",
					value: report.reduce((sum, wishlist) => sum + wishlist.skusDeleted.length, 0),
				},
				{
					metric: "Total SKUs Moved to Cart",
					value: report.reduce((sum, wishlist) => sum + wishlist.skusMovedToCart.length, 0),
				},
			];

			// Add employee filter info if provided
			if (employeecode) {
				const salesRepName = report.length > 0 ? report[0].salesRepName : "Unknown";
				summaryData.push({
					metric: "Filtered by Sales Rep",
					value: `${employeecode} (${salesRepName})`,
				});
			}

			// Add customer filter info if provided
			if (customerCRMID) {
				const customerName = report.length > 0 ? report[0].customerName : "Unknown";
				summaryData.push({
					metric: "Filtered by Customer",
					value: `${customerCRMID} (${customerName})`,
				});
			}

			// Create Excel file
			const timestamp = Date.now();
			const fileName = `wishlists_report_${timestamp}.xlsx`;

			const excelFile = await createExcelFile({
				data: excelData.length > 0 ? excelData : [{ message: "No wishlists found for the specified criteria" }],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
				],
			});

			return excelFile;
		},

		/**
		 * Calculates statistics about SKUs in quotes
		 *
		 * @param {Object} params - Query parameters
		 * @param {Date} [params.fromDate] - Start date for filtering quotes
		 * @param {Date} [params.toDate] - End date for filtering quotes
		 * @returns {Object} Statistics about SKUs in quotes
		 */
		getQuoteSkuStats: async ({ fromDate, toDate }) => {
			//✅
			const db = Services.MongoHelper.getDatabaseConnection();

			// Build match condition for quotes based on date range
			const matchConditions = {};
			if (fromDate && toDate) {
				matchConditions.createdAt = { $gte: fromDate, $lte: toDate };
			}

			// Get quotes with itemList aggregated by QUOTETYPE
			const quoteStats = await db
				.collection("quotes")
				.aggregate([
					{
						$match: matchConditions,
					},
					{
						$project: {
							quoteCRMID: 1,
							QUOTETYPE: { $ifNull: ["$QUOTETYPE", "Unknown"] },
							skuCount: { $size: { $ifNull: ["$itemList", []] } },
							createdAt: 1,
						},
					},
					{
						$group: {
							_id: "$QUOTETYPE",
							totalQuotes: { $sum: 1 },
							totalSkus: { $sum: "$skuCount" },
							quoteDetails: {
								$push: {
									quoteCRMID: "$quoteCRMID",
									skuCount: "$skuCount",
									createdAt: "$createdAt",
								},
							},
						},
					},
					{
						$project: {
							quoteType: "$_id",
							totalQuotes: 1,
							totalSkus: 1,
							avgSkusPerQuote: { $divide: ["$totalSkus", "$totalQuotes"] },
							quoteDetails: 1,
						},
					},
					{
						$sort: { quoteType: 1 },
					},
				])
				.toArray();

			// Calculate overall statistics
			const totalQuotes = quoteStats.reduce((sum, stat) => sum + stat.totalQuotes, 0);
			const totalSkus = quoteStats.reduce((sum, stat) => sum + stat.totalSkus, 0);
			const overallAvgSkus = totalQuotes > 0 ? totalSkus / totalQuotes : 0;

			// For each quote type, add cart-to-quote duration
			const skuToEarliestEvent = await Services.Analytics.getSkuToEarliestEventMap(db);

			// Calculate quote durations
			for (const statGroup of quoteStats) {
				let totalDurationMs = 0;
				let quotesWithDuration = 0;

				for (const quote of statGroup.quoteDetails) {
					// Get quote duration from cart events
					const quoteDuration = await Services.Analytics.calculateQuoteDuration(
						db,
						quote.quoteCRMID,
						quote.createdAt,
						skuToEarliestEvent
					);
					if (quoteDuration > 0) {
						totalDurationMs += quoteDuration;
						quotesWithDuration++;
						quote.durationMs = quoteDuration;
					}
				}

				// Calculate average duration for this quote type
				const avgDurationMs = quotesWithDuration > 0 ? totalDurationMs / quotesWithDuration : 0;
				const hours = Math.floor(avgDurationMs / (1000 * 60 * 60));
				const minutes = Math.floor((avgDurationMs % (1000 * 60 * 60)) / (1000 * 60));
				const seconds = Math.floor((avgDurationMs % (1000 * 60)) / 1000);

				statGroup.avgDurationFormatted = `${hours}h ${minutes}m ${seconds}s`;
				statGroup.avgDurationMinutes = avgDurationMs / (1000 * 60);
				statGroup.quotesWithDuration = quotesWithDuration;
			}

			return {
				overallStats: {
					totalQuotes,
					totalSkus,
					avgSkusPerQuote: overallAvgSkus,
				},
				quoteTypeStats: quoteStats,
			};
		},

		/**
		 * Generates an Excel file with quote SKU statistics
		 * @param {Object} params - Query parameters
		 * @param {Date} params.fromDate - Start date for filtering
		 * @param {Date} params.toDate - End date for filtering
		 * @returns {string} Path to the generated Excel file
		 */
		generateQuoteSkuStatsExcel: async ({ fromDate, toDate }) => {
			// Get quote SKU stats
			const stats = await Services.Analytics.getQuoteSkuStats({
				fromDate,
				toDate,
			});

			// Prepare summary data
			const summaryData = [
				{
					metric: "Date Range",
					value: `${fromDate.toISOString().split("T")[0]} to ${toDate.toISOString().split("T")[0]}`,
				},
				{ metric: "Total Quotes", value: stats.overallStats.totalQuotes },
				{ metric: "Total SKUs", value: stats.overallStats.totalSkus },
				{ metric: "Average SKUs Per Quote", value: stats.overallStats.avgSkusPerQuote.toFixed(2) },
			];

			// Prepare data by quote type
			const quoteTypeData = stats.quoteTypeStats.map((stat) => ({
				"Quote Type": stat.quoteType,
				"Total Quotes": stat.totalQuotes,
				"Total SKUs": stat.totalSkus,
				"Average SKUs Per Quote": stat.avgSkusPerQuote.toFixed(2),
				"Average Duration": stat.avgDurationFormatted,
				"Average Duration (Minutes)": stat.avgDurationMinutes.toFixed(2),
				"Quotes With Duration": stat.quotesWithDuration,
			}));

			// Prepare detailed data from all quotes
			const detailedData = [];
			stats.quoteTypeStats.forEach((statGroup) => {
				statGroup.quoteDetails.forEach((quote) => {
					const duration = quote.durationMs || 0;
					const hours = Math.floor(duration / (1000 * 60 * 60));
					const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
					const seconds = Math.floor((duration % (1000 * 60)) / 1000);

					detailedData.push({
						"Quote ID": quote.quoteCRMID,
						"Quote Type": statGroup.quoteType,
						"SKU Count": quote.skuCount,
						"Creation Date": quote.createdAt
							? new Date(quote.createdAt).toISOString().split("T")[0]
							: "N/A",
						Duration: duration > 0 ? `${hours}h ${minutes}m ${seconds}s` : "N/A",
						"Duration (Minutes)": duration > 0 ? (duration / (1000 * 60)).toFixed(2) : "N/A",
					});
				});
			});

			// Create Excel file
			const timestamp = Date.now();
			const fileName = `quote_sku_stats_${timestamp}.xlsx`;

			const excelFile = await createExcelFile({
				data:
					detailedData.length > 0 ? detailedData : [{ message: "No data found for the specified criteria" }],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
					{
						name: "By Quote Type",
						data: quoteTypeData.length > 0 ? quoteTypeData : [{ message: "No data found" }],
					},
				],
			});

			return excelFile;
		},

		/**
		 * Helper function to get a map of SKUs to their earliest cart events
		 * @param {Object} db - Database connection
		 * @returns {Map} Map of SKU to earliest cart event
		 */
		getSkuToEarliestEventMap: async (db) => {
			//✅
			// Get all SKUs from product events
			const skus = await db.collection("productEvents").distinct("skus", {
				event: { $in: ["addtocart", "addToCart", "movetocart", "moveToCart"] },
			});

			if (skus.length === 0) return new Map();

			// Get earliest cart event for each SKU
			const cartEvents = await db
				.collection("productEvents")
				.aggregate([
					{
						$match: {
							event: { $in: ["addtocart", "addToCart", "movetocart", "moveToCart"] },
							skus: { $in: skus },
						},
					},
					// Unwind the skus array
					{
						$unwind: {
							path: "$skus",
						},
					},
					// Sort by timestamp ascending
					{
						$sort: {
							timestamp: 1,
						},
					},
					// Group by SKU to get the earliest event
					{
						$group: {
							_id: "$skus",
							firstEvent: { $first: "$$ROOT" },
						},
					},
					// Project the fields we need
					{
						$project: {
							sku: "$_id",
							timestamp: "$firstEvent.timestamp",
							cartCRMID: "$firstEvent.cartCRMID",
							employeecode: "$firstEvent.employeecode",
						},
					},
				])
				.toArray();

			// Create map of SKU to earliest event
			const skuToEarliestEvent = new Map();
			cartEvents.forEach((event) => {
				skuToEarliestEvent.set(event.sku, event);
			});

			return skuToEarliestEvent;
		},

		/**
		 * Helper function to calculate duration between cart event and quote creation
		 * @param {Object} db - Database connection
		 * @param {string} quoteCRMID - Quote ID
		 * @param {Date} quoteCreatedAt - Quote creation timestamp
		 * @param {Map} skuToEarliestEvent - Map of SKU to earliest cart event
		 * @returns {number} Duration in milliseconds, or 0 if no valid duration
		 */
		calculateQuoteDuration: async (db, quoteCRMID, quoteCreatedAt, skuToEarliestEvent) => {
			//✅
			// Get the quote's SKUs
			const quote = await db.collection("quotes").findOne({ quoteCRMID }, { projection: { itemList: 1 } });

			if (!quote || !quote.itemList || quote.itemList.length === 0) return 0;

			// Extract SKUs from the quote
			const skus = quote.itemList.map((item) => item.sku || item.productCode).filter(Boolean);
			if (skus.length === 0) return 0;

			// Find earliest cart event for any SKU in this quote
			let earliestEvent = null;
			for (const sku of skus) {
				const event = skuToEarliestEvent.get(sku);
				if (event && (!earliestEvent || event.timestamp < earliestEvent.timestamp)) {
					earliestEvent = event;
				}
			}

			// Calculate duration if valid
			if (earliestEvent && quoteCreatedAt) {
				const durationMs = new Date(quoteCreatedAt).getTime() - new Date(earliestEvent.timestamp).getTime();
				return durationMs > 0 ? durationMs : 0;
			}

			return 0;
		},

		/**
		 * Gets information about SKUs created from Zoho
		 * @returns {Object} Information about SKUs created from Zoho
		 */
		getSkusCreatedFromZoho: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Find products with zohoSource flag
			const products = await db
				.collection("products")
				.find(
					{
						createdByZoho: true,
					},
					{
						projection: {
							code: 1,
							createdAt: 1,
						},
						sort: { createdAt: -1 },
					}
				)
				.toArray();

			// Get total count
			const totalCount = products.length;

			// Get last created SKU
			const lastCreatedSku = totalCount > 0 ? products[0] : null;

			return {
				skus: products,
				totalCount,
				lastCreatedSku: lastCreatedSku
					? {
							sku: lastCreatedSku.code,
							dateTime: lastCreatedSku.createdAt,
					  }
					: null,
			};
		},

		/**
		 * Generates an Excel file with SKUs created from Zoho
		 * @returns {string} Path to the generated Excel file
		 */
		generateSkusCreatedFromZohoExcel: async () => {
			// Get SKUs data
			const skuData = await Services.Analytics.getSkusCreatedFromZoho();

			// Format data for Excel
			const excelData = skuData.skus.map((product) => ({
				SKU: product.code,
				"Date & Time": product.createdAt ? new Date(product.createdAt).toLocaleString() : "N/A",
			}));

			// Create summary data
			const summaryData = [
				{
					metric: "Date Range",
					value: "All Time",
				},
				{ metric: "Total SKUs Created from Zoho", value: skuData.totalCount },
				{
					metric: "Last Created SKU",
					value: skuData.lastCreatedSku ? skuData.lastCreatedSku.sku : "N/A",
				},
				{
					metric: "Last Creation Date & Time",
					value: skuData.lastCreatedSku ? new Date(skuData.lastCreatedSku.dateTime).toLocaleString() : "N/A",
				},
			];

			// Create Excel file
			const timestamp = Date.now();
			const fileName = `skus_from_zoho_${timestamp}.xlsx`;

			const excelFile = await createExcelFile({
				data: excelData.length > 0 ? excelData : [{ message: "No SKUs found for the specified criteria" }],
				fileName,
				additionalSheets: [
					{
						name: "Summary",
						data: summaryData,
					},
				],
			});

			return excelFile;
		},

		/**
		 * Gets information about the last orders sent to Zoho
		 * @returns {object} Last order sent to Zoho
		 */
		getLastOrdersSentToZoho: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Find the most recent order sent to Zoho
			const quotes = await db
				.collection("quotes")
				.find({
					salesOrderId: { $exists: true },
				})
				.sort({ createdAt: -1 })
				.limit(1)
				.toArray();

			const quote = quotes[0];

			// get the so from zoho
			const zohoRes = await Services.ZohoHelper.getSalesOrderFromZoho({ salesorder_id: quote.salesOrderId });
			const so = zohoRes.data.salesorder;
			const soCustomField = so.custom_fields.find((field) => field.label === "SalesRepPID");

			// Get sales rep info
			const salesRepInfo = await db.collection("salesPersons").findOne({
				employeeCode: soCustomField?.value,
			});

			return {
				soNumber: so.salesorder_number || "N/A",
				soStatus: so.status || "N/A",
				salesRep: {
					id: soCustomField?.value || "N/A",
					name: salesRepInfo?.name || "Unknown",
				},
				customerDetails: {
					id: so.customer_id || "N/A",
					name: so.customer_name || "Unknown",
					email: so.contact_person_details[0]?.email || "N/A",
					mobile: so.contact_person_details[0]?.mobile || "N/A",
				},
				timestamp: so.created_time,
			};
		},

		/**
		 * Gets information about the last SKU details (stock, prices) updated from Zoho
		 * @param {Object} params - Query parameters
		 * @param {number} [params.limit=100] - Number of updates to return
		 * @returns {Object} Last SKU details updated from Zoho
		 */
		getLastSkuDetailsFromZoho: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Find products updated from Zoho
			const products = await db
				.collection("products")
				.find(
					{},
					{
						projection: {
							code: 1,
							stock: 1,
							price: 1,
							mrp: 1,
							updatedAt: 1,
							category: 1,
							subCategory: 1,
							name: 1,
							description: 1,
						},
						sort: { updatedAt: -1 },
						limit: 1,
					}
				)
				.toArray();

			// Get last timestamp
			const lastUpdateTimestamp = products.length > 0 ? products[0].updatedAt : null;

			return {
				lastUpdateTimestamp,
				skuDetails: products[0],
			};
		},

		/**
		 * Gets information about the last updates done with Salesforce
		 * @param {Object} params - Query parameters
		 * @param {number} [params.limit=20] - Number of updates to return
		 * @returns {Object} Last updates done with Salesforce
		 */
		getLastUpdatesWithSF: async () => {
			const db = Services.MongoHelper.getDatabaseConnection();

			// Get last hold request acceptances
			const holdRequests = await db
				.collection("holdRequests")
				.find(
					{
						status: "Hold Approved",
					},
					{
						projection: {
							holdCRMID: 1,
							category: 1,
							subCategory: 1,
							updatedAt: 1,
						},
						sort: { updatedAt: -1 },
						limit: 1,
					}
				)
				.toArray();

			// Get last discount acceptances
			const discountRequests = await db
				.collection("quotes")
				.find(
					{
						QuoteStatus: "Approved",
					},
					{
						projection: {
							quoteCRMID: 1,
							category: 1,
							subCategory: 1,
							updatedAt: 1,
						},
						sort: { updatedAt: -1 },
						limit: 1,
					}
				)
				.toArray();

			// Determine the latest timestamps
			const lastHoldReqAcceptedTimestamp =
				holdRequests.length > 0 ? holdRequests[0].lastStatusChangeTimestamp : null;

			const lastDiscountAcceptedTimestamp =
				discountRequests.length > 0 ? discountRequests[0].lastStatusChangeTimestamp : null;

			// Group hold requests by category and subcategory
			const holdRequestsByCategory = {};
			holdRequests.forEach((req) => {
				const category = req.category || "Uncategorized";
				const subCategory = req.subCategory || "Uncategorized";

				if (!holdRequestsByCategory[category]) {
					holdRequestsByCategory[category] = {};
				}

				if (!holdRequestsByCategory[category][subCategory]) {
					holdRequestsByCategory[category][subCategory] = 0;
				}

				holdRequestsByCategory[category][subCategory]++;
			});

			return {
				lastHoldReqAcceptedTimestamp,
				lastDiscountAcceptedTimestamp,
				holdRequests,
				discountRequests,
				holdRequestsByCategory,
			};
		},
	};
};
