/**
 * Products Controller
 * All products controllers will be here
 */
const { logger } = require("../utils");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Get Products
		 * This route is used to fetch products based on requirements
		 */
		"POST /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { pageNo, pageSize } = req.query;
					const {
						search,
						category,
						isDeleted,
						subCategories,
						brands,
						stock,
						color,
						price,
						brand,
						name,
						inventory,
						productType,
					} = req.body;
					const data = await Services.Products.getProducts({
						pageNo,
						pageSize,
						search,
						category,
						subCategories,
						brands,
						isDeleted,
						stock,
						color,
						price,
						brand, // this brand is for sorting in ascending or descending order
						name, // this name is for sorting in ascending or descending order
						inventory, // this inventory is for sorting in ascending or descending order
						productType,
					});
					if (data.ok) {
						return res.json({ ok: true, data: data.data, totalCount: data.totalCount });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Products Hold Request
		 * This route is used to create a hold request
		 */
		"POST /hold": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const {
						productId,
						customerId,
						code,
						category,
						subCategory,
						addressCrmId,
						salesRepPid,
						comments,
						warehouseWiseQuantity,
					} = req.body;

					// Check if productId, customerId, and salesId are provided
					if (!productId || !customerId || !salesRepPid) {
						return res.json({ ok: false, message: "ProductId, customerId, and salesRepPid are required" });
					}
					const customer = await Services.Customers.getCustomer({ _id: customerId });
					// logger.info(customer);
					// Create the hold request
					const data = await Services.Products.createHoldRequest({
						skuCode: code,
						warehouseWiseQuantity,
						duration: 48,
						category,
						subCategory,
						customerCrmId: customer.data[0].customerCRMID,
						addressCrmId,
						salesRepPid,
						architectCrmId: customer.data[0].referredByArchID,
						productId,
						customerId,
						comments,
					});
					if (data.ok) {
						return res.json({ ok: true, data: data.data, message: data.message });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Products Unhold Request
		 * This route is used to create an unhold request
		 */
		"POST /unhold": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { holdCRMID, salesRepPid } = req.body;

					// Check if both holdCRMID and salesRepPID are provided
					if (!holdCRMID || !salesRepPid) {
						return res.json({ ok: false, message: "Both holdCRMID and salesRepPID are required." });
					}

					// Create the unhold request
					const data = await Services.Products.createUnholdRequest({ holdCRMID, salesRepPid });

					if (data.ok) {
						return res.json({ ok: true, data: data.data, message: data.message });
					} else {
						return res.json({ ok: false, message: data.message });
					}
				} catch (error) {
					logger.error(error);
					return res.json({ ok: false, message: "An internal server error occurred." });
				}
			},
		},

		/**
		 * Get Hold Requests
		 * This route is used to fetch hold requests based on various parameters
		 */
		"GET /hold-requests": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Extract query parameters
					const { pageNo, pageSize, productId, customerCRMID, onHold, me, employeecode, search } = req.query;

					// Call the service to get hold requests
					const data = await Services.Products.getHoldRequest({
						pageNo,
						pageSize,
						productId,
						customerCRMID,
						onHold,
						search,
						employeecode,
						me,
					});

					// Return successful response if data is found
					if (data.ok) {
						return res.json({ ok: true, data: data.data, message: data.message });
					}

					// Return error response if no data found
					res.json({ ok: false, message: data.message });
				} catch (e) {
					// Handle any errors
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},
		/**
		 * Get Inventory Status
		 * This route is used to fetch inventory status based on sku & quantity to hold used by sf
		 */
		"GET /inventory-status": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const { sku, quantity } = req.query;
					logger.debug(`query: ${JSON.stringify({ sku, quantity })}`);
					const data = await Services.Products.getInventoryStatus({ sku, quantity: Number(quantity) });
					if (data.ok) {
						return res.json({
							sku,
							"Available Quantity":
								data.data.stock - quantity > 0 ? Math.floor(data.data.stock - quantity) : 0,
						});
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Update Hold Request
		 * This route is used to update an existing hold request used by sf
		 */
		"PUT /hold-request": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					logger.debug(JSON.stringify(req.body));
					const { holdReqCRMID, status, comments, categoryHeadName } = req.body;

					if (!holdReqCRMID) {
						return res.json({ ok: false, message: "Hold request CRM ID is required." });
					}

					if (status === undefined && categoryHeadName === undefined) {
						return res.json({
							ok: false,
							message: "status and category head name must be provided.",
						});
					}

					const data = await Services.Products.updateHoldRequest({
						holdReqCRMID,
						status,
						comments,
						categoryHeadName,
						type: "hold",
					});

					if (data.ok) {
						return res.json({ ok: true, message: data.message });
					}

					res.json({ ok: false, message: data.message });
				} catch (e) {
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},

		/**
		 * Update Unhold Request
		 * This route is used to update an existing unhold request used by sf
		 */
		"PUT /unhold-request": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const { holdReqCRMID, status, categoryHeadName, comments } = req.body;
					logger.debug(JSON.stringify(req.body));

					if (!holdReqCRMID) {
						return res.json({ ok: false, message: "Hold request CRM ID is required." });
					}

					if (status === undefined && categoryHeadName === undefined) {
						return res.json({
							ok: false,
							message: "status and category head name must be provided.",
						});
					}

					const data = await Services.Products.updateHoldRequest({
						holdReqCRMID,
						status,
						categoryHeadName,
						comments,
						type: "unhold",
					});

					if (data.ok) {
						return res.json({ ok: true, message: data.message });
					}

					res.json({ ok: false, message: data.message });
				} catch (e) {
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},

		"GET /category-brands-list": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const data = await Services.Products.getCategoryAndBrandsList();
					if (data.ok) {
						return res.json({ ok: true, data: data.data, message: data.message });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},
		/*
		 * Refresh Products Info
		 * This route is used to refresh the products info multiple products at a time.
		 */
		"POST /refresh-products": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { productIds } = req.body;
					if (productIds === undefined || productIds.length < 1) {
						res.json({ ok: false, message: "productIds cannot be empty" });
					}
					for (const sku of productIds) {
						await Services.ZohoHelper.updateProductStock({ sku });
					}
					return res.json({ ok: true, message: `Products updated Successfully` });
				} catch (error) {
					logger.error(error);
					res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Refresh all Products API
		 * This route is used to refresh all products at once.
		 */
		"GET /refresh-all-products": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { user } = req;
					if (user.employeecode === "*********") {
						Services.CronJobs.getTaxAndPricingFromZoho();
						return res.json({
							ok: true,
							message: `Products are currently being updated. Please wait 5–10 minutes for the updates to complete from Zoho`,
						});
					}
					return res.json({ ok: false, message: `Only Admin has access to do this action.` });
				} catch (error) {
					logger.error(error);
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/*
		 * Create Products From Zoho
		 * This route is used to create a products from zoho
		 */
		"POST /create-products": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const data = req.body;
					if (data === undefined || data.length < 1) {
						res.json({ ok: false, message: "products cannot be empty" });
					}
					const response = await Services.Products.createProducts(data);
					if (response.ok) {
						return res.json({ ok: true, data: response.data, message: response.message });
					}
					return res.json({ ok: false, message: response.message });
				} catch (error) {
					logger.error(error);
					res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Update Product Images
		 * This route is used to update product images via SalesRep
		 */
		"PUT /product-images": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					if (
						![
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
							"*********",
						].includes(req.user.employeecode)
					) {
						return res.json({ ok: false, message: "You Don't have Access to Upload Images." });
					}
					const { code, images, oldImage, newImage } = req.body;

					// Validate that code is provided
					if (!code) {
						return res.json({ ok: false, message: "SKU code is required" });
					}

					// Check if images is a non-empty array
					const hasImages = images && Array.isArray(images) && images.length > 0;
					const hasOldImage = oldImage !== undefined;
					const hasNewImage = newImage !== undefined;

					if (hasImages) {
						// Add operation: images is non-empty, ensure oldImage and newImage are not provided
						if (hasOldImage || hasNewImage) {
							return res.json({
								ok: false,
								message: "Cannot provide oldImage or newImage when adding new images",
							});
						}
						const data = await Services.Products.updateProductImages({
							code,
							images,
							employeecode: req.user.employeecode, // Assuming user info is available
						});
						return res.json({
							ok: data.ok,
							data: data.data,
							message: data.message,
						});
					} else if (hasOldImage && hasNewImage) {
						// Update operation: images is empty or not provided, oldImage and newImage are present
						const data = await Services.Products.updateProductImage({
							code,
							oldImage,
							newImage,
							employeecode: req.user.employeecode,
						});
						return res.json({
							ok: data.ok,
							data: data.data,
							message: data.message,
						});
					} else {
						// Insufficient data: neither condition for add nor update is met
						return res.json({
							ok: false,
							message:
								"Insufficient data: provide either images to add or oldImage and newImage to update",
						});
					}
				} catch (e) {
					logger.error(e); // Replace with proper logging in production
					return res.json({ ok: false, message: e.message });
				}
			},
		},

		/**
		 * Delete Product Images
		 * This route is used to delete product image
		 */
		"DELETE /images": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { code, image } = req.query;
					if (!code || !image) {
						return res.json({ ok: false, message: "sku code and image are required" });
					}
					const data = await Services.Products.deleteProductImage({
						code,
						image,
					});
					if (data.ok) {
						return res.json({ ok: true, data: data.data, message: data.message });
					}
					res.json({ ok: false, message: data.message });
				} catch (e) {
					logger.error(e.message);
					res.json({ ok: false, message: e.message });
				}
			},
		},

		/**
		 * Reorder Product Images
		 * This route is used to reorder product images by directly updating the product's productImages array
		 */
		"PUT /reorder-images": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { code, productImages } = req.body;

					// Validate that code and productImages are provided
					if (!code || !Array.isArray(productImages) || productImages.length === 0) {
						return res.json({
							ok: false,
							message: "SKU code and a non-empty productImages array are required",
						});
					}

					// Update the product's productImages array
					const data = await Services.Products.updateProductImagesOrder({
						code,
						productImages,
						employeecode: req.user.employeecode,
					});

					if (data.ok) {
						return res.json({
							ok: true,
							data: data.data,
							message: "Product images reordered successfully",
						});
					}

					res.json({ ok: false, message: data.message });
				} catch (e) {
					logger.error(e);
					return res.json({ ok: false, message: e.message });
				}
			},
		},

		/**
		 * Get Product Stock
		 * This route is used to get product stock
		 */
		"GET /product-stock": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const { sku } = req.query;
					const data = await Services.Products.getProductStock({ sku });
					return res.json({ ok: true, data });
				} catch (e) {
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},

		/**
		 * Sync PIM Data
		 * This route is used to manually trigger PIM synchronization
		 * Fetches files from PIM server and updates database with product information
		 */
		"POST /sync-pim": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { user } = req;
					
					// Only allow admin users to trigger PIM sync
					if (user.employeecode !== "*********") {
						return res.json({ 
							ok: false, 
							message: "Only Admin has access to perform PIM synchronization." 
						});
					}

					// Trigger PIM sync in background
					Services.CronJobs.fetchFileFromPIMAndUpdateDB()
						.then(() => {
							logger.info("PIM sync completed successfully");
						})
						.catch((error) => {
							logger.error("PIM sync failed:", error);
						});

					return res.json({
						ok: true,
						message: "PIM synchronization has been initiated. Please wait 30–45 minutes for the sync to complete. Check logs for detailed progress."
					});
				} catch (error) {
					logger.error("Error initiating PIM sync:", error);
					return res.json({ 
						ok: false, 
						message: "Failed to initiate PIM synchronization: " + error.message 
					});
				}
			},
		},
	};
};
