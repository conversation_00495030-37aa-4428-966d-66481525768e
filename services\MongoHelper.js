const { fatalError, logger } = require("../utils");
module.exports = async function ({ config /* ,Services */ }) {
	// make connection to mongodb database
	const mongoConnector = require(config.rootDir + "/helpers/mongoConnector");
	const db = await mongoConnector({ mongo: config.mongo });

	// get the database model to intialize collection references in the db variable
	let model = require(config.rootDir + "/models/" + config.mongo.dbName);

	for (let collection of model.collections) {
		db[collection] = db.collection(collection);

		// create collections if they dont exist explicitly to use transactions as it is a prerequisite
		if (!(await db.listCollections({ name: collection }, { nameOnly: true }).toArray()).length) {
			await db.createCollection(collection);
		}

		// intialize collection with default objects if any
		if (
			model.hasOwnProperty("defaultCollectionInitializations") &&
			model.defaultCollectionInitializations.hasOwnProperty(collection)
		) {
			if (!db[collection]) {
				let errMsg = `No collection reference found please check "collections" in models/${config.mongo.dbName}`;
				logger.error(errMsg);
				fatalError(errMsg);
			}
			for (let row of model.defaultCollectionInitializations[collection]) {
				await db[collection].insertOne(row).catch((e) => {
					if (e.code == 11000) {
						logger.warn("Duplicate entry found while initializing", collection);
					} else {
						logger.error("Error while initializing collection", collection, e.message);
					}
				});
			}
		}
	}

	// intialize any db indexes
	for (let collection in model.indexes) {
		if (!db[collection]) {
			let errMsg = `No collection reference found please check "collections" in models/${config.mongo.dbName}`;
			logger.error(errMsg);
			fatalError(errMsg);
		}
		let ixs = model.indexes[collection];
		for (let ekIndex of ixs) {
			if (ekIndex.length > 0) {
				let y = await db[collection].createIndex(ekIndex[0], ekIndex[1] || {});
				logger.info("creation of index: ", y);
			}
		}
	}

	return {
		getDatabaseConnection: () => db,
	};
};
