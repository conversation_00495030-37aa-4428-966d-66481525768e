/**
 * Defines the routes for cart-related operations
 */
const { logger } = require("../utils");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Handles the request to add a product to the cart
		 */
		"POST /add-product": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract required parameters from the request body
					const { customerCRMID, addressCRMID, architectCRMID = "", itemList, salesRepPID } = req.body;
					// Validate that all required parameters are present
					if (!customerCRMID || !addressCRMID || !itemList || !salesRepPID) {
						const missingParams = [];
						if (!customerCRMID) missingParams.push("customerCRMID");
						if (!addressCRMID) missingParams.push("addressCRMID");
						if (!itemList) missingParams.push("itemList");
						if (!salesRepPID) missingParams.push("salesRepPID");

						return res.status(400).json({
							ok: false,
							message: `Missing required parameters: ${missingParams.join(", ")}`,
						});
					}

					// Validate itemList
					if (!Array.isArray(itemList) || itemList.length === 0) {
						return res.json({ ok: false, message: "itemList must be a non-empty array" });
					}

					// Call the service to add product to cart
					const result = await Services.Carts.addProductToCart({
						customerCRMID,
						addressCRMID,
						architectCRMID,
						itemList,
						salesRepPID,
					});

					// Return the result
					return res.json(result);
				} catch (error) {
					// Log any errors that occur
					logger.error(`Error in add-product handler: ${error.message}`);
					// Return a generic error message
					return res
						.status(500)
						.json({ ok: false, message: "An error occurred while adding product to the cart" });
				}
			},
		},

		/**
		 * Handles the request to update a product in the cart
		 */
		"POST /update-product": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract required parameters from the request body
					const { cartCRMID, itemList, salesRepPID } = req.body;
					// Validate that all required parameters are present
					if (!itemList || !salesRepPID || !cartCRMID) {
						const missingParams = [];
						if (!itemList) missingParams.push("itemList");
						if (!salesRepPID) missingParams.push("salesRepPID");
						if (!cartCRMID) missingParams.push("cartCRMID");

						return res.status(400).json({
							ok: false,
							message: `Missing required parameters: ${missingParams.join(", ")}`,
						});
					}
					logger.debug(itemList);
					// Validate that itemList is an array
					if (!Array.isArray(itemList) || itemList.length === 0) {
						return res.status(400).json({ ok: false, message: "itemList must be a non-empty array" });
					}

					// Call the service to update the product in the cart
					const result = await Services.Carts.updateProductInCart({
						cartCRMID,
						itemList,
						salesRepPID,
					});

					// Return the result
					return res.json(result);
				} catch (error) {
					// Log any errors that occur
					logger.error(`Error in update-product handler: ${error.message}`);
					// Return a generic error message
					return res.status(500).json({ ok: false, message: "An error occurred while updating the cart" });
				}
			},
		},

		/**
		 * Handles the request to get cart data for a customer
		 */
		"GET /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract customerId from query parameters
					const { customerCRMID, customerId } = req.query;

					// Validate that customerId is present
					if (!customerCRMID && !customerId) {
						return res.status(400).json({ ok: false, message: "customerCRMID or customerId is required" });
					}

					// Call the service to get the cart data
					const result = await Services.Carts.getCartByCustomerId({ customerCRMID, customerId });

					// Return the result
					return res.json(result);
				} catch (error) {
					// Log any errors that occur
					logger.error(error);
					// Return a generic error message
					return res
						.status(500)
						.json({ ok: false, message: "An error occurred while fetching the cart data" });
				}
			},
		},

		/**
		 * Handles the request to delete a cart item for a customer
		 */
		"POST /delete": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract cartIds from request body
					const { productNames, cartCRMID, customerId } = req.body;

					if (
						!productNames ||
						!Array.isArray(productNames) ||
						productNames.length === 0 ||
						(!cartCRMID && !customerId)
					) {
						return res.status(400).json({
							ok: false,
							message: "productNames must be a non-empty array and cartCRMID or customerId is required",
						});
					}

					// Call the service to delete multiple cart items
					const result = await Services.Carts.deleteMultipleCartItems({
						productNames,
						cartCRMID,
						customerId,
					});

					// Return the result
					return res.json(result);
				} catch (error) {
					// Log any errors that occur
					logger.error(error);
					// Return a generic error message
					return res
						.status(500)
						.json({ ok: false, message: "An error occurred while deleting the cart items" });
				}
			},
		},

		/**
		 * Handles the request to update the unstructured discount or warehouse_id in cart
		 */
		"POST /update-unstructured-discount-warehouseid-comment": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { cartCRMID, unstructuredDiscount, warehouse_ids, code, comment } = req.body;
					if (
						(unstructuredDiscount === undefined && !warehouse_ids && !comment) ||
						(!cartCRMID && !code && !comment) ||
						(comment && !cartCRMID)
					) {
						return res.status(400).json({
							ok: false,
							message:
								"cartCRMID is required, and either unstructuredDiscount or warehouse_ids or comment is required, or code is required if unstructuredDiscount or warehouse_id is provided",
						});
					}
					const result = await Services.Carts.updateUnstructuredDiscountWarehouseIdComment({
						cartCRMID,
						unstructuredDiscount,
						code,
						warehouse_ids,
						comment,
					});
					return res.json(result);
				} catch (error) {
					logger.error(error);
					return res.json({
						ok: false,
						message: "An error occurred while updating the unstructured discount",
					});
				}
			},
		},

		/**
		 * Tracks product-related events such as adding to cart or removing from cart
		 */
		"POST /track-event": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract required parameters from the request body
					const { employeecode, cartCRMID, wishlistCRMID, skus, event, metadata } = req.body;

					// Validate required parameters
					if (!employeecode || !event) {
						return res.status(400).json({
							ok: false,
							message: "employeecode and event are required parameters",
						});
					}

					// Validate event type and required parameters
					const eventType = event;
					const validEvents = [
						"addToCart",
						"removeFromCart",
						"moveToWishlist",
						"moveToCart",
						"addToWishlist",
						"removeFromWishlist"
					];

					if (!validEvents.includes(eventType)) {
						return res.status(400).json({
							ok: false,
							message: "Invalid event type. Must be one of: addToCart, removeFromCart, moveToWishlist, moveToCart, addToWishlist, removeFromWishlist",
						});
					}

					// Validate required parameters based on event type
					const missingParams = [];
					
					// Cart-related events require cartCRMID
					if (["addTocart", "removeFromcart", "moveToWishlist", "moveToCart"].includes(eventType) && !cartCRMID) {
						missingParams.push("cartCRMID");
					}

					// Wishlist-related events require wishlistCRMID
					if (["moveToWishlist", "moveToCart", "addToWishlist", "removeFromWishlist"].includes(eventType) && !wishlistCRMID) {
						missingParams.push("wishlistCRMID");
					}

					// Validate skus array
					if (!skus || !Array.isArray(skus) || skus.length === 0) {
						missingParams.push("skus (non-empty array)");
					}

					if (missingParams.length > 0) {
						return res.status(400).json({
							ok: false,
							message: `Missing required parameters for ${event}: ${missingParams.join(", ")}`,
						});
					}

					// Call the Tracker service to log the product event
					const result = await Services.Tracker.logProductEvent({
						employeecode,
						cartCRMID: cartCRMID || undefined,
						wishlistCRMID: wishlistCRMID || undefined,
						skus,
						event: eventType,
						metadata: metadata || {}
					});

					// Return the result
					return res.json(result);
				} catch (error) {
					// Log any errors that occur
					logger.error(`Error in track-event handler: ${error.message}`);
					// Return a generic error message
					return res.status(500).json({ 
						ok: false, 
						message: "An error occurred while tracking the product event" 
					});
				}
			},
		},
	};
};
