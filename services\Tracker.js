const { logger } = require("../utils");
/**
 * Tracker Service
 * Logs various events (e.g., login, logout, etc.) for tracking purposes.
 */
module.exports = async function ({ Services, config }) {
	return {
		/**
		 * Logs an event.
		 * @param {Object} params - The event details.
		 * @param {string} params.employeecode - The employee code of the user.
		 * @param {string} params.event - The event type (e.g., "login", "logout").
		 * @param {Date} params.timestamp - The timestamp of the event.
		 */
		logEvent: async ({ employeecode, event, timestamp, metadata = {} }) => {
			const db = Services.MongoHelper.getDatabaseConnection();
			await db.collection("loginLogs").insertOne({
				employeecode,
				event,
				timestamp,
			});
		},

		/**
		 * Logs a product-related event.
		 * @param {Object} params - The event details.
		 * @param {string} params.employeecode - The employee code of the user.
		 * @param {string} [params.cartCRMID] - The cart CRM ID (required for cart-related events).
		 * @param {string} [params.wishlistCRMID] - The wishlist CRM ID (required for wishlist-related events).
		 * @param {string[]} params.skus - Array of product SKU codes.
		 * @param {string} params.event - The event type (e.g., "addToCart", "removeFromCart", "moveToWishlist", "moveToCart", "addToWishlist", "removeFromWishlist").
		 * @param {Object} [params.metadata] - Additional metadata for the event.
		 * @returns {Object} Result of the operation.
		 */
		logProductEvent: async ({ employeecode, cartCRMID, wishlistCRMID, skus, event, metadata = {} }) => {
			const db = Services.MongoHelper.getDatabaseConnection();
			
			// Validate required parameters
			if (!employeecode || !event) {
				return {
					ok: false,
					message: "employeecode and event are required"
				};
			}
			
			// Validate skus parameter
			if (!skus || !Array.isArray(skus) || skus.length === 0) {
				return {
					ok: false,
					message: "skus must be a non-empty array"
				};
			}
			
			// Validate event type
			const eventType = event;
			
			// Validate required parameters based on event type
			const missingParams = [];
			
			// Cart-related events require cartCRMID
			if (["addTocart", "removeFromcart", "moveToWishlist", "moveToCart"].includes(eventType) && !cartCRMID) {
				missingParams.push("cartCRMID");
			}
			
			// Wishlist-related events require wishlistCRMID
			if (["moveToWishlist", "moveToCart", "addToWishlist", "removeFromWishlist"].includes(eventType) && !wishlistCRMID) {
				missingParams.push("wishlistCRMID");
			}
			
			if (missingParams.length > 0) {
				return {
					ok: false,
					message: `Missing required parameters for ${event}: ${missingParams.join(", ")}`
				};
			}
			
			// Insert the product event
			const result = await db.collection("productEvents").insertOne({
				employeecode,
				...(cartCRMID && { cartCRMID }),
				...(wishlistCRMID && { wishlistCRMID }),
				skus,
				event: eventType,
				metadata,
				timestamp: new Date()
			});
			
			// Create a more descriptive message based on the event type
			let message;
			
			switch (eventType) {
				case "addToCart":
					message = `Successfully logged products added to cart ${cartCRMID}`;
					break;
				case "removeFromCart":
					message = `Successfully logged products removed from cart ${cartCRMID}`;
					break;
				case "moveToWishlist":
					message = `Successfully logged products moved to wishlist ${wishlistCRMID} from cart ${cartCRMID}`;
					break;
				case "moveToCart":
					message = `Successfully logged products moved to cart ${cartCRMID} from wishlist ${wishlistCRMID}`;
					break;
				case "addToWishlist":
					message = `Successfully logged products added to wishlist ${wishlistCRMID}`;
					break;
				case "removeFromWishlist":
					message = `Successfully logged products removed from wishlist ${wishlistCRMID}`;
					break;
				default:
					message = `Successfully logged ${eventType} event`;
			}
			
			return {
				ok: true,
				message,
				eventId: result.insertedId
			};
		},

		/**
		 * Fetches event details within a date range.
		 * @param {Object} params - The query parameters.
		 * @param {Date} params.fromDate - The start date of the range.
		 * @param {Date} params.toDate - The end date of the range.
		 * @param {string} [params.event] - Filter by event type (optional).
		 * @returns {Array} List of event logs.
		 */
		getLoginLogs: async ({ fromDate, toDate }) => {
			const db = Services.MongoHelper.getDatabaseConnection();
			const query = { timestamp: { $gte: fromDate, $lte: toDate } };
			logger.info(query);
			
			const logs = await db.collection("loginLogs")
				.aggregate([
					{ $match: query },
					{ $sort: { timestamp: 1 } },
					{
						$group: {
							_id: "$employeecode",
							logs: { $push: "$$ROOT" }
						}
					}
				])
				.toArray();

			return logs;
		},
		
		/**
		 * Fetches product event details within a date range.
		 * @param {Object} params - The query parameters.
		 * @param {Date} params.fromDate - The start date of the range.
		 * @param {Date} params.toDate - The end date of the range.
		 * @param {string} [params.event] - Filter by event type (optional).
		 * @param {string} [params.employeecode] - Filter by employee code (optional).
		 * @param {string} [params.cartCRMID] - Filter by cart CRM ID (optional).
		 * @param {string[]} [params.skus] - Filter by product SKUs (optional).
		 * @param {string} [params.wishlistCRMID] - Filter by wishlist CRM ID (optional).
		 * @returns {Array} List of product event logs.
		 */
		getProductEventDetails: async ({ fromDate, toDate, event, employeecode, cartCRMID, skus, wishlistCRMID }) => {
			const db = Services.MongoHelper.getDatabaseConnection();
			
			// Build query based on provided parameters
			const query = { timestamp: { $gte: fromDate, $lte: toDate } };
			if (event) query.event = event;
			if (employeecode) query.employeecode = employeecode;
			if (cartCRMID) query.cartCRMID = cartCRMID;
			if (wishlistCRMID) query.wishlistCRMID = wishlistCRMID;
			
			// Handle skus parameter
			if (skus && Array.isArray(skus) && skus.length > 0) {
				query.skus = { $in: skus };
			}

			const results = await db.collection("productEvents").find(query).toArray();
			
			return {
				ok: true,
				events: results
			};
		}
	};
};
