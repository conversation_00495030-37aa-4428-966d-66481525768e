/**
 * Address Service Module
 * This module provides functionality for managing customer addresses.
 */

const { logger } = require("../utils");
const { ObjectId } = require("mongodb");

module.exports = async function ({ Services }) {
	return {
		/**
		 * Creates new addresses for a customer
		 * @param {Array} addresses - An array of address objects containing address details
		 * @returns {Object} Result of the address creation operation
		 */
		createAddress: async ({ addressList, customerId }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				if (!Array.isArray(addressList) || addressList.length === 0) {
					return { ok: false, message: "Invalid addresses input" };
				}

				// Check if the customer exists
				const customer = await db
					.collection("customers")
					.findOne({ _id: ObjectId.createFromHexString(customerId) });
				if (!customer) {
					return { ok: false, message: "Customer not found" };
				}

				// If any address is set as primary, unset any existing primary address
				const hasPrimaryAddress = addressList.some((address) => address.primaryAddress);
				if (hasPrimaryAddress) {
					await db
						.collection("addresses")
						.updateMany(
							{ customerId: customerId, primaryAddress: true },
							{ $set: { primaryAddress: false, updatedAt: new Date() } }
						);
				}

				// Prepare addresses for insertion
				const addressesToInsert = addressList.map((address) => ({
					...{ primaryAddress: address?.primaryAddress || false },
					...{ street: address?.street || "" },
					...{ state: address?.state || "" },
					...{ city: address?.city || "" },
					...{ country: address?.country || "" },
					...{ pincode: address?.pincode || "" },
					...{ addressCRMID: address?.addressCRMID || "" },
					...{ code: address?.code || "" },
					...{ region: address?.region || "" },
					...{ houseNo: address?.houseNo || "" },
					customerId, // Append customerId to every address
					createdAt: new Date(),
					updatedAt: new Date(),
				}));

				// Insert the new addresses
				const result = await db.collection("addresses").insertMany(addressesToInsert);

				// Check if the addresses were successfully inserted
				if (result.acknowledged) {
					return { ok: true, message: "Addresses created successfully", data: result };
				} else {
					return { ok: false, message: "Failed to create addresses" };
				}
			} catch (error) {
				// Log the error and return a generic error message
				logger.error("Error in createAddress service", error);
				return { ok: false, message: "An error occurred while creating the addresses" };
			}
		},
		/**
		 * Updates an existing address
		 * @param {string} addressId - The ID of the address to update
		 * @param {Object} addressUpdate - The object containing the fields to update
		 * @returns {Object} Result of the address update operation
		 */
		updateAddress: async ({ addressId, addressList, isExportCustomer=false }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				logger.debug(addressId, addressList);
				// Check if the address exists
				const existingAddress = await db
					.collection("addresses")
					.findOne({ _id: ObjectId.createFromHexString(addressId) });
				if (!existingAddress) {
					return { ok: false, message: "Address not found" };
				}

				// If updating to primary address, unset any existing primary address for the customer
				if (addressList[0]?.primaryAddress) {
					await db
						.collection("addresses")
						.updateMany(
							{ customerId: existingAddress.customerId, primaryAddress: true },
							{ $set: { primaryAddress: false, updatedAt: new Date() } }
						);
				}

				// Set region and code based on isExportCustomer flag
				let region = addressList[0]?.region;
				let code = addressList[0]?.code;
				
				// Apply business logic for export customers
				if (isExportCustomer === true) {
					// Export customers: force code as 99 and region as DM
					region = 'DM';
					code = '99';
				} else {
					// Non-export customers: cannot have code 99 and region DM
					// If they currently have 99/DM, change to preferred values MH/27
					if (region === 'DM' || code === '99') {
						region = 'MH';
						code = '27';
					}
					// If no region/code provided, set preferred defaults for non-export customers
					else if (!region && !code) {
						region = 'MH';
						code = '27';
					}
				}

				// Update the address
				const result = await db.collection("addresses").updateOne(
					{ _id: ObjectId.createFromHexString(addressId) },
					{
						$set: {
							...(addressList[0]?.street && { street: addressList[0].street }),
							...(addressList[0]?.city && { city: addressList[0].city }),
							...(addressList[0]?.state && { state: addressList[0].state }),
							...(addressList[0]?.country && { country: addressList[0].country }),
							...(addressList[0]?.pincode && { pincode: addressList[0].pincode }),
							...(region && { region }),
							...(code && { code }),
							...(addressList[0]?.houseNo && { houseNo: addressList[0].houseNo }),
							...(addressList[0]?.primaryAddress !== undefined && {
								primaryAddress: addressList[0].primaryAddress,
							}),
							updatedAt: new Date(),
						},
					}
				);
				// Check if the address was successfully updated
				if (result.modifiedCount === 1) {
					return { ok: true, message: "Address updated successfully", data: result };
				} else if (result.matchedCount === 1) {
					return { ok: true, message: "No changes were made to the address", data: result };
				} else {
					return { ok: false, message: "Failed to update address" };
				}
			} catch (error) {
				// Log the error and return a generic error message
				logger.error("Error in updateAddress service", error);
				return { ok: false, message: "An error occurred while updating the address" };
			}
		},
		/**
		 * Fetches addresses for a specific customer
		 * @param {string} customerId - The ID of the customer
		 * @returns {Object} Result of the address fetch operation
		 */
		getAddressesByCustomerId: async (customerId) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Fetch addresses for the customer
				const addresses = await db.collection("addresses").find({ customerId: customerId }).toArray();

				if (addresses.length > 0) {
					return { ok: true, message: "Addresses fetched successfully", data: addresses };
				} else {
					return { ok: true, message: "No addresses found for this customer", data: [] };
				}
			} catch (error) {
				// Log the error and return a generic error message
				logger.error("Error in getAddressesByCustomerId service", error);
				return { ok: false, message: "An error occurred while fetching addresses" };
			}
		},

		/**
		 * Deletes an address for a specific customer
		 * @param {string} addressId - The ID of the address to delete
		 * @returns {Object} Result of the address deletion operation
		 */
		deleteAddress: async (addressId) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Delete the address
				const result = await db
					.collection("addresses")
					.deleteOne({ _id: ObjectId.createFromHexString(addressId) });

				if (result.deletedCount === 1) {
					return { ok: true, message: "Address deleted successfully" };
				} else {
					return { ok: false, message: "Address not found or already deleted" };
				}
			} catch (error) {
				// Log the error and return a generic error message
				logger.error("Error in deleteAddress service", error);
				return { ok: false, message: "An error occurred while deleting the address" };
			}
		},

		updateOrCreateAddress: async (db, addressList, now) => {
			const addressCollection = db.collection("addresses");
			const bulkOps = [];

			for (const address of addressList) {
				// Standardize address fields only if they are null or undefined
				address.state = address.state ?? "Maharashtra";
				address.country = address.country ?? "INDIA";
				address.code = address.code ?? "27";
				address.city = address.city ?? "Mumbai";
				address.region = address.region ?? "MH";
				address.houseNo = address.houseNo ?? "";
				address.pincode = address.pincode ?? "400013";
				address.street = address.street ?? "";
				address.addressCRMID = address.AddressCRMID ?? address.addressCRMID;
				delete address.AddressCRMID;

				// Ensure customerId is string to prevent mismatches
				const customerId = address.customerId.toString();

				// Use upsert instead of separate findOne and insertOne/updateOne
				bulkOps.push({
					updateOne: {
						filter: { customerId },
						update: {
							$set: { ...address, updatedAt: now },
							$setOnInsert: { createdAt: now , primaryAddress: true}, // Only set createdAt on insert
						},
						upsert: true, // Insert if not found, update otherwise
					},
				});
			}

			// Perform bulk write operation
			if (bulkOps.length > 0) {
				await addressCollection.bulkWrite(bulkOps);
			}
		},

		getStates: async () => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const states = await db.collection("stateDescriptions").find({}).toArray();
			return states;
		},

		getStateByPinCode: async ({ pincode, state, district }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				if (pincode) {
					// Create regex pattern to match pincode
					const pincodePattern = new RegExp(`^${pincode}`);

					const stateData = await db
						.collection("statePincode")
						.find({
							pincode: { $regex: pincodePattern },
						})
						.toArray();

					if (!stateData.length) {
						return { ok: false, message: "No state found for this pincode" };
					}

					return {
						ok: true,
						data: stateData,
					};
				}

				if (state) {
					// Convert state to title case (first letter capital, rest small)
					const formattedState = state
						.toLowerCase()
						.split(" ")
						.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
						.join(" ");

					const districts = await db
						.collection("statePincode")
						.distinct("district", { state: formattedState });

					if (!districts.length) {
						return { ok: false, message: "No districts found for this state" };
					}

					return {
						ok: true,
						data: districts,
					};
				}

				if (district) {
					// Convert district to title case (first letter capital, rest small)
					const formattedDistrict = district
						.toLowerCase()
						.split(" ")
						.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
						.join(" ");

					const pincodes = await db
						.collection("statePincode")
						.find({ district: formattedDistrict })
						.toArray();

					if (!pincodes.length) {
						return { ok: false, message: "No pincodes found for this district" };
					}

					return {
						ok: true,
						data: pincodes,
					};
				}

				return { ok: false, message: "Invalid parameters provided" };
			} catch (error) {
				logger.error("Error in getStateByPinCode service", error);
				return { ok: false, message: "An error occurred while fetching state data" };
			}
		},
	};
};
