/**
 * Customers Service
 * Customers service functions defined here
 */

const { logger } = require("../utils");
const { ObjectId } = require("mongodb");

module.exports = async function ({ Services }) {
	return {
		create: async (insertObj) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const { name, email, mobile, customerSource, referredByArchID, otherDetails, salesRepPID, code } =
					insertObj;
				const currentTime = new Date();
				const insertDocument = {
					...(code ? { code } : {}),
					...(name ? { name } : {}),
					...(email ? { email } : {}),
					...(mobile ? { mobile: mobile.toString() } : {}),
					...(customerSource ? { customerSource } : {}),
					...(salesRepPID ? { salesRepPID } : {}),
					...(referredByArchID ? { referredByArchID } : {}),
					...(otherDetails ? { otherDetails } : {}),
					...(insertObj.isArchitect ? { firmName: insertObj.firmName } : {}),
					...(insertObj.isArchitect ? { isArchitect: true } : { isArchitect: false }),
					...(insertObj.code ? { isAnonymous: true } : { isAnonymous: false }),
					...(insertObj.firmName ? { firmName: insertObj.firmName } : {}),
					...(insertObj.isArchitect ? { architectCRMID: insertObj.architectCRMID } : {}),
					...(insertObj.customerCRMID ? { customerCRMID: insertObj.customerCRMID } : {}),
					...(insertObj.gst_treatment ? {"gst_treatment": "overseas"} : {"gst_treatment": "customer"}),
					isExportCustomer: insertObj.isExportCustomer || false,
					sourceSystem: "FOYR",
					createdAt: currentTime,
					updatedAt: currentTime,
					whatsappConcent: insertObj.whatsappConcent || false,
					visitAgain: insertObj.visitAgain || false,
					createdBy: salesRepPID || "",
					// New fields - these will NOT be sent to SF/Zoho
					...(insertObj.gender ? { gender: insertObj.gender } : {}),
					...(insertObj.profession ? { profession: insertObj.profession } : {}),
					...(insertObj.purchase ? { purchase: insertObj.purchase } : {}),
					...(insertObj.noUpdates !== undefined ? { noUpdates: insertObj.noUpdates } : { noUpdates: false }),
					...(insertObj.promotionalInsiderUpdate !== undefined ? { promotionalInsiderUpdate: insertObj.promotionalInsiderUpdate } : { promotionalInsiderUpdate: false }),
					...(insertObj.invitesToEvents !== undefined ? { invitesToEvents: insertObj.invitesToEvents } : { invitesToEvents: false }),
					...(insertObj.preferredModeOfCommunicationForUpdates !== undefined ? { preferredModeOfCommunicationForUpdates: insertObj.preferredModeOfCommunicationForUpdates } : { preferredModeOfCommunicationForUpdates: false }),
					...(insertObj.referredByWhom ? { referredByWhom: insertObj.referredByWhom } : {}), // This uses customerCRMID as per requirement
					...(insertObj.relation ? { relation: insertObj.relation } : {}),
				};

				// Handle timeSpent as string with history in timeSpentList
				if (insertObj.timeSpent) {
					insertDocument.timeSpent = insertObj.timeSpent;
					insertDocument.timeSpentList = [{
						duration: insertObj.timeSpent,
						createdAt: currentTime
					}];
				}

				// Handle voiceOfTheCustomer as string with history in voiceOfCustomerList
				if (insertObj.voiceOfTheCustomer) {
					insertDocument.voiceOfTheCustomer = insertObj.voiceOfTheCustomer;
					insertDocument.voiceOfCustomerList = [{
						remark: insertObj.voiceOfTheCustomer,
						createdAt: currentTime
					}];
				}

				let re1 = await db.collection("customers").insertOne(insertDocument);

				if (!re1.acknowledged) {
					return { ok: false };
				}

				// Start the session for the newly created customer
				const sessionResult = await Services.Customers.session({
					isActive: true,
					customer: re1.insertedId.toString(),
				});

				if (!sessionResult.ok) {
					logger.error(`Failed to start session for  ${insertObj.isArchitect ? "Architect" : "Customer"}`);
				}

				return { ok: true, re1 };
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		getCustomer: async (payload) => {
			try {
				let { _id, isArchitect, email, mobile, search, code, isAnonymous, createdBy } = payload;
				const db = await Services.MongoHelper.getDatabaseConnection();
				if (_id && ObjectId.isValid(_id)) {
					_id = ObjectId.createFromHexString(_id);
				}
				const query = {
					...(code ? { code } : {}),
					...(_id ? { _id } : {}),
					...(isArchitect === true || isArchitect === false ? { isArchitect } : {}),
					...(email ? { email } : {}),
					...(mobile ? { mobile: { $regex: mobile.toString().replace(/^\+/, ""), $options: "i" } } : {}),
					...(isAnonymous === "true" ? { isAnonymous: true } : { isAnonymous: false }),
					...(createdBy ? { createdBy, isAnonymous: false } : {}),
					...(search
						? {
								$or: [
									{ name: { $regex: search, $options: "i" } },
									{ email: { $regex: search, $options: "i" } },
									{ mobile: { $regex: search, $options: "i" } },
									{ firmName: { $regex: search, $options: "i" } },
								],
						  }
						: {}),
				};

				let pipeline = [{ $match: query }];

				if (isAnonymous === "true") {
					pipeline = [
						...pipeline,
						{
							$addFields: {
								customerIdString: { $toString: "$_id" },
							},
						},
						{
							$lookup: {
								from: "sessions",
								localField: "customerIdString",
								foreignField: "customer",
								as: "sessionInfo",
							},
						},
						{
							$unwind: {
								path: "$sessionInfo",
								preserveNullAndEmptyArrays: true, // Keeps customers even if no session exists
							},
						},
						{ $project: { customerIdString: 0 } }, // Remove temporary field
					];
				}

				let re1 = await db.collection("customers").aggregate(pipeline).toArray();

				if (!re1 || re1.length === 0) {
					return { ok: false, message: "No customers found" };
				}
				return { ok: true, data: re1 };
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		updateCustomer: async (query) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const { code, _id, timeSpent, voiceOfTheCustomer } = query;
				delete query.code;
				delete query._id;
				delete query.addressList;
				delete query.timeSpent;
				delete query.voiceOfTheCustomer;
				
				const currentTime = new Date();
				
				// Extract and prepare the update object with new fields
				const updateObj = {
					...query,
					updatedAt: currentTime,
					isAnonymous: false,
				};

				// Ensure new fields are properly handled
				if (query.gender !== undefined) updateObj.gender = query.gender;
				if (query.profession !== undefined) updateObj.profession = query.profession;
				if (query.purchase !== undefined) updateObj.purchase = query.purchase;
				if (query.noUpdates !== undefined) updateObj.noUpdates = query.noUpdates;
				if (query.promotionalInsiderUpdate !== undefined) updateObj.promotionalInsiderUpdate = query.promotionalInsiderUpdate;
				if (query.invitesToEvents !== undefined) updateObj.invitesToEvents = query.invitesToEvents;
				if (query.preferredModeOfCommunicationForUpdates !== undefined) updateObj.preferredModeOfCommunicationForUpdates = query.preferredModeOfCommunicationForUpdates;
				if (query.referredByWhom !== undefined) updateObj.referredByWhom = query.referredByWhom; // Uses customerCRMID as per requirement
				if (query.relation !== undefined) updateObj.relation = query.relation;

				// Prepare the update operation
				const updateOperation = { $set: updateObj };

				// Handle timeSpent as string with history push to timeSpentList
				if (timeSpent) {
					updateOperation.$set.timeSpent = timeSpent;
					updateOperation.$push = {
						...updateOperation.$push,
						timeSpentList: {
							duration: timeSpent,
							createdAt: currentTime
						}
					};
				}

				// Handle voiceOfTheCustomer as string with history push to voiceOfCustomerList
				if (voiceOfTheCustomer) {
					updateOperation.$set.voiceOfTheCustomer = voiceOfTheCustomer;
					updateOperation.$push = {
						...updateOperation.$push,
						voiceOfCustomerList: {
							remark: voiceOfTheCustomer,
							createdAt: currentTime
						}
					};
				}

				let re1 = await db
					.collection("customers")
					.updateOne(code ? { code: code } : { _id: ObjectId.createFromHexString(_id) }, updateOperation);
				// logger.debug(re1);
				if (!re1 || re1.length === 0) {
					return { ok: false, message: "No customers found" };
				}
				return { ok: true, data: re1 };
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},
		session: async (insertObj) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const { isActive, customer } = insertObj;
				const currentTime = new Date();

				const existingSession = await db.collection("sessions").findOne({ customer });

				// remove items from cart for anonymous customers when session is ended
				if (isActive === false) {
					const customerData = await db
						.collection("customers")
						.findOne({ _id: ObjectId.createFromHexString(customer) });
					if (customerData.isAnonymous) {
						// empty cart for anonymous customer
						await db
							.collection("carts")
							.updateOne({ customerCRMID: customerData.customerCRMID }, { $set: { itemList: [] } });
						// remove quotes for anonymous customer
						await db.collection("quotes").deleteMany({ customerCRMID: customerData.customerCRMID });
					}
				}
				if (!existingSession) {
					const newSession = {
						customer,
						isActive,
						createdAt: currentTime,
						updatedAt: currentTime,
						sessionStartTime: currentTime,
						sessionEndTime: isActive ? null : currentTime,
					};

					const result = await db.collection("sessions").insertOne(newSession);
					return { ok: true, data: result };
				} else {
					const updateData = {
						isActive,
						updatedAt: currentTime,
					};

					if (!isActive) {
						updateData.sessionEndTime = currentTime;
					}

					const result = await db.collection("sessions").updateOne({ customer }, { $set: updateData });

					return { ok: true, data: result };
				}
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},
		getActiveCustomers: async (search) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const searchRegex = new RegExp(search, "i");
				const activeCustomers = await db
					.collection("sessions")
					.aggregate([
						{ $match: { isActive: true } },
						{
							$addFields: {
								customer: { $toObjectId: "$customer" },
							},
						},
						{
							$lookup: {
								from: "customers",
								localField: "customer",
								foreignField: "_id",
								as: "customerDetails",
							},
						},
						{ $unwind: "$customerDetails" },
						{
							$replaceRoot: { newRoot: "$customerDetails" },
						},
						{
							$match: {
								...(search
									? {
											$or: [
												{ name: searchRegex },
												{ email: searchRegex },
												{ mobile: searchRegex },
												{ code: searchRegex },
											],
									  }
									: {}),
							},
						},
						{
							$sort: { updatedAt: -1 },
						},
					])
					.toArray();

				if (activeCustomers.length > 0) {
					return { ok: true, data: activeCustomers, size: activeCustomers.length };
				} else {
					return { ok: false, message: "No Active Customer Found" };
				}
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		getSalesPersons: async () => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const data = await db.collection("salesPersons").find({}).toArray();
				return { ok: true, data };
			} catch (e) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},
		/**
		 * Updates or creates an entity (customer or architect) in the database
		 * @param {string} type - The type of entity ('customer' or 'architect')
		 * @param {Object} data - The entity data to be updated or created
		 * @returns {Object} Result of the update or create operation
		 */
		updateEntity: async (type, data) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const now = new Date();
				const {
					customerCRMID,
					architectCRMID,
					name,
					email,
					mobile,
					addressList,
					referredByArchID,
					firmName,
					bdmName,
					code,
					// New fields
					gender,
					profession,
					purchase,
					timeSpent,
					voiceOfTheCustomer,
					noUpdates,
					promotionalInsiderUpdate,
					invitesToEvents,
					preferredModeOfCommunicationForUpdates,
					referredByWhom,
					relation,
				} = data;
				const collection = db.collection("customers");
				const isArchitect = type === "architect";
				const crmId = isArchitect ? architectCRMID : customerCRMID;

				// Check if the entity already exists
				const existingEntity = await collection.findOne({
					...(email ? { email } : {}),
					mobile: mobile.toString(),
				});
				// logger.debug(existingEntity);

				// Prepare the entity data
				const entityData = {
					name,
					email,
					mobile: mobile.toString(),
					...(isArchitect ? { firmName, bdmName, isArchitect: true } : { isArchitect: false }),
					...(code ? { isAnonymous: true, code } : { isAnonymous: false }),
					updatedAt: now,
					referredByArchID,
					// New fields - these will NOT be sent to SF/Zoho
					...(gender ? { gender } : {}),
					...(profession ? { profession } : {}),
					...(purchase ? { purchase } : {}),
					...(noUpdates !== undefined ? { noUpdates } : {}),
					...(promotionalInsiderUpdate !== undefined ? { promotionalInsiderUpdate } : {}),
					...(invitesToEvents !== undefined ? { invitesToEvents } : {}),
					...(preferredModeOfCommunicationForUpdates !== undefined ? { preferredModeOfCommunicationForUpdates } : {}),
					...(referredByWhom ? { referredByWhom } : {}), // Uses customerCRMID as per requirement
					...(relation ? { relation } : {}),
				};

				// Prepare the update operation
				const updateOperation = {
					$set: {
						...entityData,
						[isArchitect ? "architectCRMID" : "customerCRMID"]: crmId,
					},
					$setOnInsert: {
						createdAt: now,
						createdBy: "SF",
					},
				};

				// Handle timeSpent as string with history push to timeSpentList
				if (timeSpent) {
					updateOperation.$set.timeSpent = timeSpent;
					updateOperation.$push = {
						...updateOperation.$push,
						timeSpentList: {
							duration: timeSpent,
							createdAt: now
						}
					};
				}

				// Handle voiceOfTheCustomer as string with history push to voiceOfCustomerList
				if (voiceOfTheCustomer) {
					updateOperation.$set.voiceOfTheCustomer = voiceOfTheCustomer;
					updateOperation.$push = {
						...updateOperation.$push,
						voiceOfCustomerList: {
							remark: voiceOfTheCustomer,
							createdAt: now
						}
					};
				}

				// If the entity doesn't exist, create a new one
				const insertResult = await collection.updateOne(
					{
						...(email ? { email } : {}),
						mobile: mobile.toString(),
					},
					updateOperation,
					{ upsert: true }
				);
				// logger.debug(insertResult);

				if (!insertResult.acknowledged) {
					return { ok: false, message: `Failed to create ${type}` };
				}
				// logger.debug(insertResult, existingEntity)
				// If it's a customer, update or create the associated addressList
				addressList?.forEach(async (address) => {
					address.customerId = existingEntity?._id
						? existingEntity._id.toString()
						: insertResult.upsertedId.toString();
				});
				if (!isArchitect) {
					await Services.Address.updateOrCreateAddress(db, addressList, now);
				}

				return {
					ok: true,
					message: `${type} created successfully`,
					data: insertResult.insertedId,
				};
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "An error occurred while updating the entity",
				};
			}
		},
		getCustomersByQuery: async (query) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const res = await db.collection("customers").aggregate(query).toArray();
				return res;
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		submitFeedback: async (feedback) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const { rating, comment, customerCRMID } = feedback;
			if (rating < 1 || rating > 5) {
				return { ok: false, message: "Rating must be between 1 and 5" };
			}
			if (!customerCRMID) {
				return { ok: false, message: "Customer CRM ID is required" };
			}
			const customer = await db.collection("customers").findOne({ customerCRMID });
			if (!customer) {
				return { ok: false, message: "Customer not found" };
			}
			const feedbackObj = {
				rating,
				comment: comment || "",
				customerCRMID,
				createdAt: new Date(),
				updatedAt: new Date(),
			};
			const result = await db.collection("customerFeedbacks").insertOne(feedbackObj);
			if (!result.acknowledged) {
				return { ok: false, message: "Failed to submit feedback" };
			}
			return { ok: true, message: "Feedback submitted successfully", data: result };
		},

		getLeads: async (filter, options) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const { page, limit, sort } = options;

				const leads = await db
					.collection("customers")
					.find(filter)
					.sort(sort)
					.skip((page - 1) * limit)
					.limit(limit)
					.toArray();

				const totalLeads = await db.collection("customers").countDocuments(filter);

				return {
					ok: true,
					data: {
						leads,
						totalLeads,
						totalPages: Math.ceil(totalLeads / limit),
						currentPage: page,
					},
				};
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: error.message,
				};
			}
		},

		addNote: async (note) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const { customerCRMID, employeecode, content } = note;

			if (!content) {
				return { ok: false, message: "Note content is required" };
			}

			const noteObj = {
				customerCRMID,
				employeecode,
				content,
				createdAt: new Date(),
				updatedAt: new Date(),
			};

			const result = await db.collection("notes").insertOne(noteObj);

			if (!result.acknowledged) {
				return { ok: false, message: "Failed to add note" };
			}

			return { ok: true, message: "Note added successfully", data: result };
		},

		getNotes: async ({ customerCRMID, employeecode }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const filter = {};

			if (customerCRMID) {
				filter.customerCRMID = customerCRMID;
			}

			if (employeecode) {
				filter.employeecode = employeecode;
			}

			const notes = await db.collection("notes").find(filter).sort({ updatedAt: -1 }).toArray();

			if (!notes || notes.length === 0) {
				return { ok: false, message: "No notes found" };
			}

			return { ok: true, data: notes };
		},

		updateNote: async (note) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const { noteId, content } = note;

			if (!content) {
				return { ok: false, message: "Note content is required" };
			}

			const result = await db
				.collection("notes")
				.findOneAndUpdate(
					{ _id: ObjectId.createFromHexString(noteId) },
					{ $set: { content, updatedAt: new Date() } },
					{ returnOriginal: false }
				);

			return { ok: true, message: "Note updated successfully", data: result };
		},

		deleteNote: async (noteId) => {
			const db = await Services.MongoHelper.getDatabaseConnection();

			const result = await db.collection("notes").deleteOne({ _id: ObjectId.createFromHexString(noteId) });

			if (result.deletedCount === 0) {
				return { ok: false, message: "Note not found" };
			}

			return { ok: true, message: "Note deleted successfully" };
		},
	};
};
