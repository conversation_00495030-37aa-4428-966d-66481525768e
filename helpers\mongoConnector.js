let mongodb = require("mongodb");
var MongoClient = mongodb.MongoClient;
const f = require("util").format;
const { logger } = require("../utils");

module.exports = async function (config) {
	try {
		let options = {
			// useNewUrlParser: true,
			...(config.mongo.replicaSet && { replicaSet: config.mongo.replicaSet }),
			minPoolSize: config.mongo.poolSize || 15,
			// useUnifiedTopology: true,
		};
		let url = config.mongo.dnsSeedList === true ? "mongodb+srv://" : "mongodb://";
		if (true && !!config.mongo.pw) {
			const user = encodeURIComponent(config.mongo.un);
			const password = encodeURIComponent(config.mongo.pw);

			const authMechanism = config.mongo.authMechanism || "SCRAM-SHA-1";
			url = f(
				"%s%s:%s@%s/?authMechanism=%s&authSource=admin",
				url,
				user,
				password,
				config.mongo.host,
				authMechanism
			);
		} else {
			url += config.mongo.host;
		}
		let client = new MongoClient(url, options);
		const db = client.db(config.mongo.dbName);
		db.isConnected = () => client.isConnected();
		db._client = client;
		db.close = () => {
			return client.close(true);
		};
		return db;
	} catch (err) {
		logger.error(err.stack);
		let printableConf = config.mongo;
		delete printableConf.pw;
		logger.error("Mongodb cannot connect to: ", printableConf);
		throw err;
	}
};
