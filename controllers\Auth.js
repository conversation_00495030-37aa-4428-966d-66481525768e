/**
 * Auth Controller
 * admin auth endpoints
 */

const { logger } = require("../utils");
module.exports.routes = function ({ Services, config }) {
	return {
		"POST /login": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const base64Credentials = req.headers.authorization.split(" ")[1];
					const decodedCredentials = Buffer.from(base64Credentials, "base64").toString("utf-8");
					const [email, password] = decodedCredentials.split(":");
					if (!email || !password) {
						return res.status(400).json({ ok: false, message: "Email and password are required" });
					}
					const user = await Services.SalesPersons.login({ email, password });
					if (!user.ok) {
						return res.status(400).json({ ok: false, message: user.message });
					}
					const { token, userDetails } = user;

					// Log the login event
					await Services.Tracker.logEvent({
						employeecode: userDetails.employeecode,
						event: "login",
						timestamp: new Date(),
					});

					return res.status(200).json({ ok: true, token, userDetails });
				} catch (e) {
					logger.error(e);
					return res.status(500).json({ ok: false, message: "Internal server error" });
				}
			},
		},

		"GET /me": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const user = req.user;
					return res.status(200).json({ ok: true, user });
				} catch (e) {
					logger.error(e);
					return res.status(500).json({ ok: false, message: "Internal server error" });
				}
			},
		},

		/**
		 * Update Sales Person mobile number & name
		 * @param {string} mobile - The new mobile number
		 * @param {string} name - The new name
		 * @returns {Object} Result of the update operation
		 */
		"PUT /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { mobile, name } = req.body;
					if (!mobile) {
						return res.status(400).json({ ok: false, message: "Mobile number is required" });
					}
					const user = req.user;
					const updatedUser = await Services.SalesPersons.updateMobile({ _id: user._id, mobile, name });
					return res.status(200).json({ ok: true, user: updatedUser });
				} catch (e) {
					logger.error(e);
					return res.status(500).json({ ok: false, message: "Internal server error" });
				}
			},
		},
		
		/**
		 * Logout Sales Person
		 * Logs the logout event and invalidates the user token
		 * @returns {Object} Result of the logout operation
		 */
		"POST /logout": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Log the logout event
					await Services.Tracker.logEvent({
						employeecode: req.user.employeecode,
						event: "logout",
						timestamp: new Date(),
					});

					// Remove the token from the oidcs collection
					await Services.SalesPersons.logout({ token: req.token });

					return res.status(200).json({ ok: true, message: "Logged out successfully" });
				} catch (e) {
					logger.error(e);
					return res.status(500).json({ ok: false, message: "Internal server error" });
				}
			},
		},

	};
};
