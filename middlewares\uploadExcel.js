const multer = require("multer");

// Configure storage
const storage = multer.memoryStorage();

// Create multer instance with file size limit
const upload = multer({
	storage: storage,
	limits: { fileSize: 5 * 1024 * 1024 }, // 5MB file size limit
}).single("file"); // Expect a single file with field name 'file'

module.exports = (req, res, next) => {
	upload(req, res, (err) => {
		if (err instanceof multer.MulterError) {
			// A Multer error occurred when uploading
			return res.status(400).json({ ok: false, message: "File upload error: " + err.message });
		} else if (err) {
			// An unknown error occurred
			return res.status(500).json({ ok: false, message: "Unknown error: " + err.message });
		}
		// Everything went fine
		next();
	});
};
