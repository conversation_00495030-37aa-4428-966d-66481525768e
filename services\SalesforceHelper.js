const axios = require("axios");
const { logger } = require("../utils");

module.exports = async function ({ Services, config }) {
	return {
		registerCustomer: async function (customerData) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_RegisterCustomer/*`,
					headers: {
						"Content-Type": "application/json",
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							name: customerData.name,
							email: customerData.email,
							mobile: customerData.mobile,
							...(customerData.customerSource ? { customerSource: customerData.customerSource } : {}),
							...(customerData.salesRepPID ? { salesRepPID: customerData.salesRepPID } : {}),
							...(customerData.referredByArchID
								? { referredByArchID: customerData.referredByArchID }
								: {}),
							sourceSystem: "FOYR",
							addressList: customerData.addressList.map((address) => ({
								street: address.street,
								city: address.city,
								pincode: address.pincode,
							})),
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error registering customer in Salesforce:", error);
				throw error;
			}
		},

		registerArchitect: async function (architectData) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_RegisterArchitect/*`,
					headers: {
						"Content-Type": "application/json",
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							name: architectData.name,
							email: architectData.email,
							mobile: architectData.mobile,
							firmName: architectData.firmName,
							sourceSystem: "FOYR",
							salesRepPID: architectData.salesRepPID,
							addressList: architectData.addressList.map((address) => ({
								street: address.street,
								city: address.city,
								pincode: address.pincode,
							})),
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error registering architect in Salesforce:", error);
				throw error;
			}
		},

		fetchCustomer: async function ({ customerCRMID, email, mobile }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_FetchCustomer/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							customerid: customerCRMID ? customerCRMID : "",
							email: email ? email : "",
							mobile: mobile ? mobile : "",
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error fetching customer in Salesforce:", error);
				throw error;
			}
		},
		updateAddressAndCustomer: async function ({
			customerCRMID,
			name,
			email,
			mobile,
			addressList,
			referredByArchID,
		}) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_AddressAndCustomerDetailsUpdate/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							addressDetailsUpdate: {
								AddressCRMID: addressList[0].addressCRMID,
								street: addressList[0].street,
								city: addressList[0].city,
								pincode: addressList[0].pincode,
							},
							customerDetailsUpdate: {
								CustomerCRMID: customerCRMID,
								name: name,
								mobile: mobile,
								email: email,
								referredByArchID,
							},
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error updating address and customer in Salesforce:", error);
				throw error;
			}
		},
		updateArchitect: async function ({ architectCRMID, name, email, mobile, firmName, addressList }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_ArchitectUpdate/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							name: name,
							mobile: mobile,
							firmname: firmName,
							email: email,
							customerid: architectCRMID,
							Address: addressList.map((address) => {
								return {
									Street: address.street,
									Pincode: address.pincode,
									City: address.city,
								};
							}),
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error updating architect in Salesforce:", error);
				throw error;
			}
		},

		createAddress: async function ({ address, customerCRMID }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_CreateAddress/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							street: address[0].street,
							city: address[0].city,
							pincode: address[0].pincode,
							customerid: customerCRMID,
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error creating address in Salesforce:", error);
				throw error;
			}
		},

		holdRequest: async function ({
			skuCode,
			quantity,
			duration,
			category,
			subCategory,
			customerCrmId,
			comments = "",
			addressCrmId,
			salesRepPid,
			architectCrmId = "",
			SKUurl,
		}) {
			try {
				logger.debug(
					`holdReq payload` +
						JSON.stringify({
							skuCode,
							quantity,
							duration,
							category,
							subCategory,
							customerCrmId,
							addressCrmId,
							salesRepPid,
							architectCrmId,
							comments,
							SKUurl,
						})
				);
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_HoldRequest/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						skuCode,
						quantity,
						duration,
						category,
						subCategory,
						customerCrmId,
						addressCrmId,
						salesRepPid,
						architectCrmId,
						comments,
						SKUurl,
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error creating hold request in Salesforce:", error);
				throw error;
			}
		},

		unholdRequest: async function ({ holdCRMID, salesRepPID }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_UnholdRequest/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						holdCRMID,
						salesRepPID,
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error creating unhold request in Salesforce:", error);
				throw error;
			}
		},
		fetchArchitect: async function ({ email, mobile, customerid }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_FetchArchitect/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: {
							email: email || "",
							mobile: mobile || "",
							customerid: customerid || "",
						},
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error fetching architect from Salesforce:", error);
				return {
					ok: false,
					message: "An error occurred while fetching architect data",
				};
			}
		},
		createWishlist: async function ({
			customerCrmId,
			addressCrmId,
			architectCrmId = "",
			requestType,
			itemList,
			salesPersonPid,
		}) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_CreateWishlist/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						customerCrmId,
						addressCrmId,
						architectCrmId,
						requestType,
						itemList,
						salesPersonPid,
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error creating wishlist in Salesforce:", error);
				throw error;
			}
		},
		updateWishlist: async function ({ WISHLISTID, itemList, salesPersonPid }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_UpdateWishlist/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: { WISHLISTID, itemList, salesPersonPid },
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error updating cart in Salesforce:", error);
				throw error;
			}
		},
		createCart: async function ({
			customerCrmId,
			addressCrmId,
			architectCrmId = "",
			requestType,
			itemList,
			salesPersonPid,
		}) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_CreateCart/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						customerCrmId,
						addressCrmId,
						architectCrmId,
						requestType,
						itemList,
						salesPersonPid,
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error creating cart in Salesforce:", error);
				throw error;
			}
		},
		updateCart: async function ({ CartID, itemList, salesPersonPid }) {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_UpdateCart/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						req: { CartID, itemList, salesPersonPid },
					},
				});

				return response.data;
		},
		createQuote: async function ({
			customerCrmId,
			addressCrmId,
			architectCrmId = "",
			category,
			itemList,
			salesPersonPid,
			wishlistID_cartID,
			quoteURL,
			DISCOUNT_PERCENTAGE,
			discount_AMOUNT,
			grand_total_WITHOUT_DISCOUNT,
			grand_total_WITH_DISCOUNT,
			comments,
			QUOTETYPE,
		}) {
			try {
				logger.debug(
					`quotePayload: ` +
						JSON.stringify({
							customerCrmId,
							addressCrmId,
							architectCrmId,
							category,
							itemList,
							salesPersonPid,
							wishlistID_cartID,
							quoteURL,
							DISCOUNT_PERCENTAGE,
							discount_AMOUNT,
							grand_total_WITHOUT_DISCOUNT,
							grand_total_WITH_DISCOUNT,
							comments,
							QUOTETYPE,
						})
				);
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_CreateQuote/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						customerCrmId,
						addressCrmId,
						architectCrmId,
						category,
						itemList,
						salesPersonPid,
						wishlistID_cartID,
						quoteURL,
						DISCOUNT_PERCENTAGE,
						discount_AMOUNT,
						grand_total_WITHOUT_DISCOUNT,
						grand_total_WITH_DISCOUNT,
						comments,
						QUOTETYPE,
					},
				});

				return response;
			} catch (error) {
				logger.error("Error creating quote in Salesforce:", error);
				throw error;
			}
		},
		tagArchitect: async function ({ categorySalesCRMID, architectCRMID = "", salesRepPID }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_TagArchitecture/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						categorySalesCRMID,
						architectCRMID,
						salesRepPID,
					},
				});

				return response;
			} catch (error) {
				logger.error("Error tagging architect in Salesforce:", error);
				throw error;
			}
		},
		proceedToCheckout: async function ({ categorySalesCRMID }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_ProceedToCheckout/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						categorySalesCRMID,
						status: "PROCEED TO CHECK OUT",
					},
				});

				return response.data;
			} catch (error) {
				logger.error("Error proceeding to checkout in Salesforce:", error);
				throw error;
			}
		},
		orderStatusUpdate: async function ({ categorySalesCRMID, quoteCRMID }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_OrderStatusUpdate/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						categorySalesCRMID,
						status: "order placed",
						quoteCRMID,
					},
				});
				return response.data;
			} catch (error) {
				logger.error("Error updating order status in Salesforce:", error);
				throw error;
			}
		},

		nullifyHoldRequest: async function ({ holdCRMID, salesRepPID, status }) {
			try {
				const response = await axios({
					method: "POST",
					url: `${config.sf.baseUrl}/services/apexrest/Nilaya_Nullifyhold/*`,
					headers: {
						Authorization: `Bearer ${config.sf.token}`,
					},
					data: {
						holdCRMID,
						salesRepPID,
						status,
					},
				});
				return response.data;
			} catch (error) {
				logger.error("Error updating nullify hold request in Salesforce:", error);
				throw error;
			}
		},
	};
};
