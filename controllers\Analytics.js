/**
 * Analytics Controller
 * This module handles all Analytics related routes
 */

// Import the logger utility for logging purposes
const { logger } = require("../utils");
const { ObjectId } = require("mongodb");
const axios = require("axios");
const nodemailer = require("nodemailer");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Product Count
		 * This route is used to get the count of the products
		 */
		"GET /productCount": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// get active product count
					const count = await Services.Analytics.productsCount();
					// Send a success response
					res.status(201).json({
						ok: true,
						message: "Product Count Fetched successfully",
						productCount: count,
					});
				} catch (error) {
					logger.error("Error getting products Count", error);

					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},
		/**
		 * Product Count with Images
		 * This route is used to get the count of the products containing images
		 */
		"GET /productCountImages": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// get active product count
					const counts = await Services.Analytics.productsCountImages();
					// Send a success response
					res.status(201).json({
						ok: true,
						message: "Product Count Fetched successfully",
						counts,
					});
				} catch (error) {
					logger.error("Error getting products With Images Count", error);

					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},
		/**
		 * Categories Count
		 * This route is used to get the count and list of unique product categories
		 */
		"GET /categoriesCount": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { count, categories } = await Services.Analytics.categoriesCount();
					res.status(200).json({
						ok: true,
						message: "Categories Count Fetched successfully",
						categoriesCount: count,
						categories,
					});
				} catch (error) {
					logger.error("Error getting categories count", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},
		/**
		 * Subcategories Count
		 * This route is used to get the count and list of unique product subcategories
		 */
		"GET /subCategoriesCount": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { count, subCategories } = await Services.Analytics.subCategoriesCount();
					res.status(200).json({
						ok: true,
						message: "Subcategories Count Fetched successfully",
						subCategoriesCount: count,
						subCategories,
					});
				} catch (error) {
					logger.error("Error getting subcategories count", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate Active Products as Excel
		 * This route generates an Excel file containing active product data, uploads it to S3,
		 * stores the link in the database with a 30-day TTL, and informs the user that the process is underway.
		 */
		"GET /GenerateProductsExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"], // Middleware to ensure the user is logged in
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					// Check if a report was generated in the last 3 hours
					const threeHoursAgo = new Date(Date.now() - 3 * 60 * 60 * 1000);
					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType: "productsList",
						createdAt: { $gte: threeHoursAgo },
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent report is already available.",
							data: recentReport,
						});
					}

					// Immediately respond to the client to avoid blocking while processing large data
					res.status(200).json({
						ok: true,
						message: "Your report is under progress and will be available in 5-10 mins.",
					});

					// Generate Excel file with active products data
					const excelPath = await Services.Analytics.generateProductsExcel();

					// Extract the file name from the generated file path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for uploading the file to S3
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "productsList",
						fileName: fileName,
					});

					// Upload the generated Excel file to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists for automatic deletion of records after 30 days
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Insert the file URL into the database with an expiration time of 30 days
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Auto-delete after 30 days
							reportType: "productsList",
						});
					}

					// Remove the locally stored Excel file to free up space
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error downloading active products", error);
					// Don't try to send a response here if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get Active Products Excel
		 * This route fetches the link to the Excel file containing active products data
		 */

		"GET /GetProductsExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"], // Middleware to ensure the user is logged in
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					// Fetch the latest active products Excel file URL from the database
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType: "productsList" })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({ ok: false, message: "No active products report found" });
					}

					// Send the file URL to the client
					res.status(200).json({
						ok: true,
						message: "Active products report fetched successfully",
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching active products report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		// "GET login"

		/**
		 * Get quote history
		 * This route fetches the quote history for all
		 */

		// "GET /quoteHistory": {
		// 	version: "v1.0",
		// 	localMiddlewares: ["isLoggedIn"], // Middleware to ensure the user is logged in
		// 	handler: async (req, res) => {
		// 		try {
		// 			const db = Services.MongoHelper.getDatabaseConnection();

		// 	sort_column = "created_time",
		// 	sort_order = "D",
		// 	// salesorder_id,
		// 	// SalesRepPID,
		// 		const url = salesorder_id
		// 			? `https://www.zohoapis.in/books/v3/salesorders/${salesorder_id}?organization_id=${config.zoho.organization_id}`
		// 			: SalesRepPID
		// 			? `https://www.zohoapis.in/books/v3/salesorders?organization_id=${config.zoho.organization_id}&cf_salesreppid=${SalesRepPID}&sort_column=${sort_column}&sort_order=${sort_order}`
		// 			: `https://www.zohoapis.in/books/v3/salesorders?organization_id=${config.zoho.organization_id}&customer_id=${customer_id}&sort_column=${sort_column}&sort_order=${sort_order}`;

		// 		const response = await axios({
		// 			method: "GET",
		// 			url,
		// 			headers: {
		// 				Authorization: `Zoho-oauthtoken ${config.zoho.sales_order_token}`,
		// 				"Content-Type": "application/json",
		// 			},
		// 		});
		// 		return { ok: true, data: response.data };

		// 			// Fetch the quote history from the database
		// 			const quoteHistory = await db.collection("quoteHistory").find({}).toArray();

		// 			if (!quoteHistory.length > 0) {
		// 				return res.status(404).json({ ok: false, message: "No quote history found" });
		// 			}

		// 			// Send the quote history to the client
		// 			res.status(200).json({
		// 				ok: true,
		// 				message: "Quote history fetched successfully",
		// 				data: quoteHistory,
		// 			});
		// 		} catch (error) {
		// 			logger.error("Error fetching quote history", error);
		// 			res.status(500).json({ ok: false, message: error.message });
		// 		}
		// 	},
		// },
		/**
		 * User Login Details
		 * This route fetches user login details within a date range.
		 */
		"GET /userLoginLogs": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate } = req.query;
					if (!fromDate || !toDate) {
						return res.status(400).json({ ok: false, message: "fromDate and toDate are required" });
					}

					const loginDetails = await Services.Tracker.getLoginLogs({
						fromDate: new Date(fromDate),
						toDate: new Date(toDate),
					});

					res.status(200).json({
						ok: true,
						message: "User login details fetched successfully",
						data: loginDetails,
					});
				} catch (error) {
					logger.error("Error fetching user login details", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Quotes Analytics
		 * This route fetches statistics about quotes and their conversion to sales orders
		 */
		"GET /quotesAnalytics": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					let { fromDate, toDate, so } = req.query;

					// Validate date parameters
					if (fromDate && toDate) {
						fromDate = new Date(fromDate);
						toDate = new Date(toDate);
					}

					const db = Services.MongoHelper.getDatabaseConnection();

					// Build date filter if dates are provided
					const dateFilter = fromDate && toDate ? { createdAt: { $gte: fromDate, $lte: toDate } } : {};

					// Get total quotes count
					const totalQuotes = await db.collection("quotes").countDocuments(dateFilter);

					// Build sales order filter based on 'so' parameter
					let salesOrderFilter = {};
					if (so !== undefined) {
						const hasSO = so === "true";
						salesOrderFilter = hasSO
							? { salesOrderId: { $exists: true } }
							: { salesOrderId: { $exists: false } };
					}

					// Get quotes based on filter
					const quotesQuery = db.collection("quotes").aggregate([
						{
							$match: {
								...dateFilter,
								...salesOrderFilter,
							},
						},
						{
							$lookup: {
								from: "salesOrders",
								localField: "salesOrderId",
								foreignField: "salesorder_id",
								as: "salesOrderDetails",
							},
						},
						{
							$lookup: {
								from: "salesPersons",
								localField: "salesRepPID",
								foreignField: "employeecode",
								as: "salesRepDetails",
							},
						},
						{
							$lookup: {
								from: "customers",
								localField: "customerCRMID",
								foreignField: "customerCRMID",
								as: "customerDetails",
							},
						},
						{
							$unwind: {
								path: "$salesOrderDetails",
								preserveNullAndEmptyArrays: true,
							},
						},
						{
							$unwind: {
								path: "$salesRepDetails",
								preserveNullAndEmptyArrays: true,
							},
						},
						{
							$unwind: {
								path: "$customerDetails",
								preserveNullAndEmptyArrays: true,
							},
						},
						{
							$project: {
								soNumber: "$salesOrderDetails.salesorder_number",
								salesRepPID: 1,
								salesRepName: "$salesRepDetails.name",
								customerName: "$customerDetails.name",
								createdAt: 1,
								itemList: 1,
								quoteCRMID: 1,
								category: 1,
								subCategory: 1,
								discount_AMOUNT: 1,
								grand_total_WITHOUT_DISCOUNT: 1,
								grand_total_WITH_DISCOUNT: 1,
								QuoteStatus: 1,
								hasSalesOrder: { $cond: [{ $ifNull: ["$salesOrderId", false] }, true, false] },
								QUOTETYPE: 1,
							},
						},
					]);

					const quotes = await quotesQuery.toArray();

					// Count quotes with sales orders
					const quotesWithSOCount = await db.collection("quotes").countDocuments({
						...dateFilter,
						salesOrderId: { $exists: true },
					});

					res.status(200).json({
						ok: true,
						message: "Quotes analytics fetched successfully",
						data: {
							totalQuotes,
							quotesWithSOCount,
							filteredQuotesCount: quotes.length,
							quotes,
						},
					});
				} catch (error) {
					logger.error("Error fetching quotes analytics", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate Quotes Analytics Excel
		 * This route generates an Excel file with quotes data and their conversion to sales orders
		 */
		"GET /GenerateQuotesAnalyticsExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { so, fromDate, toDate } = req.query;
					const db = Services.MongoHelper.getDatabaseConnection();

					// Check if a report with the same filter parameters was generated in the last 20 minutes
					const twentyMinsAgo = new Date(Date.now() - 20 * 60 * 1000);

					// Create a filter parameters string for the report type
					const reportType = `quotesAnalytics`;

					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType,
						createdAt: { $gte: twentyMinsAgo },
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent quotes analytics report is already available.",
							data: recentReport,
						});
					}

					// Immediately respond to the client to avoid blocking while processing
					res.status(200).json({
						ok: true,
						message: "Your quotes analytics report is being generated and will be available in 5-10 mins.",
					});

					// Generate Excel file with the filter parameters
					const excelPath = await Services.Analytics.generateQuotesAnalyticsExcel({
						so: so === "true" ? true : so === "false" ? false : undefined,
						fromDate: fromDate ? new Date(fromDate) : undefined,
						toDate: toDate ? new Date(toDate) : undefined,
					});

					// Extract filename from path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for uploading the file to S3
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "quotesAnalytics",
						fileName: fileName,
					});

					// Upload the generated Excel file to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists for automatic deletion of records after 30 days
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Insert the file URL into the database with an expiration time of 30 days
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Auto-delete after 30 days
							reportType,
							filterParams: { so, fromDate, toDate },
						});
					}

					// Remove the locally stored Excel file to free up space
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error generating quotes analytics excel", error);
					// Don't try to send a response here if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get Quotes Analytics Excel
		 * This route fetches the latest quotes analytics Excel report
		 */
		"GET /GetQuotesAnalyticsExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					// Fetch the latest quotes analytics Excel file URL from the database
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType: "quotesAnalytics" })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({ ok: false, message: "No quotes analytics report found" });
					}

					// Send the file URL to the client
					res.status(200).json({
						ok: true,
						message: "Quotes analytics report fetched successfully",
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching quotes analytics report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * User Activity Stats
		 * This route fetches activity statistics for a particular user in a date range
		 */
		"GET /userActivityStats": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { employeecode, date } = req.query;

					if (!employeecode) {
						return res.status(400).json({ ok: false, message: "Employee code is required" });
					}

					// If specific date is provided, set range to that day
					// Otherwise default to current day
					const targetDate = date ? new Date(date) : new Date();
					const startDate = new Date(targetDate);
					startDate.setHours(0, 0, 0, 0);

					const endDate = new Date(targetDate);
					endDate.setHours(23, 59, 59, 999);

					// Get user activity stats
					const stats = await Services.Analytics.getUserActivityStats({
						employeecode,
						startDate,
						endDate,
					});

					res.status(200).json({
						ok: true,
						message: "User activity stats fetched successfully",
						data: stats,
					});
				} catch (error) {
					logger.error("Error fetching user activity stats", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate User Activity Excel
		 * Generates an Excel report with user activity data
		 */
		"GET /GenerateUserActivityExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { employeecode, startDate, endDate } = req.query;

					if (!employeecode || !startDate || !endDate) {
						return res
							.status(400)
							.json({ ok: false, message: "Employee code, start date, and end date are required" });
					}

					const db = Services.MongoHelper.getDatabaseConnection();

					// Format filter parameters
					const filterParams = { employeecode, startDate, endDate };
					const reportType = `userActivityStats`;

					// Check if a recent report exists
					const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000);
					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType,
						createdAt: { $gte: twelveHoursAgo },
						filterParams,
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent user activity report is already available",
							data: recentReport,
						});
					}

					// Immediately respond to avoid blocking
					res.status(200).json({
						ok: true,
						message: "Your user activity report is being generated and will be available in 5-10 mins.",
					});

					// Generate Excel file
					const excelPath = await Services.Analytics.generateUserActivityExcel({
						employeecode,
						startDate: new Date(startDate),
						endDate: new Date(endDate),
					});

					// Extract filename from path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for S3 upload
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "userActivityStats",
						fileName,
					});

					// Upload to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Store file information
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
							reportType,
							filterParams,
						});
					}

					// Cleanup local file
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error generating user activity excel", error);
					// Don't try to send a response here if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get User Activity Excel
		 * Fetches the latest user activity Excel report
		 */
		"GET /GetUserActivityExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// const { employeecode } = req.query;

					// if (!employeecode) {
					// 	return res.status(400).json({ ok: false, message: "Employee code is required" });
					// }

					const db = Services.MongoHelper.getDatabaseConnection();
					const reportType = `userActivityStats`;

					// Fetch the latest Excel file
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({ ok: false, message: "No user activity report found" });
					}

					res.status(200).json({
						ok: true,
						message: "User activity report fetched successfully",
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching user activity report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Sales Order Contribution Stats
		 * Fetches details about who contributed to a sales order
		 */
		"GET /salesOrderContributionStats": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { salesorder_id } = req.query;

					if (!salesorder_id) {
						return res.status(400).json({ ok: false, message: "Sales order ID is required" });
					}

					// Get sales order contribution stats
					const stats = await Services.Analytics.getSalesOrderContributionStats({
						salesorder_id,
					});

					res.status(200).json({
						ok: true,
						message: "Sales order contribution stats fetched successfully",
						data: stats,
					});
				} catch (error) {
					logger.error("Error fetching sales order contribution stats", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate Sales Order Contribution Excel
		 * Generates an Excel report showing user contributions to sales orders
		 */
		"GET /GenerateSalesOrderContributionExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { startDate, endDate } = req.query;

					if (!startDate || !endDate) {
						return res.status(400).json({ ok: false, message: "Start date and end date are required" });
					}

					const db = Services.MongoHelper.getDatabaseConnection();

					// Format filter parameters
					const filterParams = { startDate, endDate };
					const reportType = "salesOrderContributionStats";

					// Check if a recent report exists
					const twentyMinsAgo = new Date(Date.now() - 20 * 60 * 1000);
					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType,
						createdAt: { $gte: twentyMinsAgo },
						filterParams,
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent sales order contribution report is already available",
							data: recentReport,
						});
					}

					// Immediately respond to avoid blocking
					res.status(200).json({
						ok: true,
						message:
							"Your sales order contribution report is being generated and will be available in 5-10 mins.",
					});

					// Generate Excel file
					const excelPath = await Services.Analytics.generateSalesOrderContributionExcel({
						startDate: new Date(startDate),
						endDate: new Date(endDate),
					});

					// Extract filename from path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for S3 upload
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "salesOrderContributions",
						fileName,
					});

					// Upload to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Store file information
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
							reportType,
							filterParams,
						});
					}

					// Cleanup local file
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error generating sales order contribution excel", error);
					// Don't try to send a response here if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get Sales Order Contribution Excel
		 * Fetches the latest sales order contribution Excel report
		 */
		"GET /GetSalesOrderContributionExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();
					const reportType = "salesOrderContributionStats";

					// Fetch the latest Excel file
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({ ok: false, message: "No sales order contribution report found" });
					}

					res.status(200).json({
						ok: true,
						message: "Sales order contribution report fetched successfully",
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching sales order contribution report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Total Carts Open Report
		 * This route fetches the report of open carts with details
		 * Can be filtered by date range, employee code, or customer ID
		 */
		"GET /totalCartsOpen": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate, employeecode, customerCRMID } = req.query;

					// Check if any filter is provided at all
					if (!fromDate && !toDate && !employeecode && !customerCRMID) {
						return res.status(400).json({
							ok: false,
							message:
								"At least one filter criteria is required: a date range, employeecode, or customerCRMID",
						});
					}

					// If either of the date parameters is provided, ensure both are present
					if ((fromDate || toDate) && (!fromDate || !toDate)) {
						return res.status(400).json({
							ok: false,
							message: "Both fromDate and toDate must be provided when using a date filter",
						});
					}

					// Determine which criteria are set
					const hasDates = fromDate && toDate;
					const hasEmployee = Boolean(employeecode);
					const hasCRM = Boolean(customerCRMID);

					// Ensure that only one criteria is provided:
					// either dates only, employeecode only, or customerCRMID only.
					const criteriaCount = [hasDates, hasEmployee, hasCRM].filter(Boolean).length;

					if (criteriaCount !== 1) {
						return res.status(400).json({
							ok: false,
							message:
								"Only one filter criteria must be provided: either a date range (both fromDate and toDate), employeecode, or customerCRMID",
						});
					}

					const report = await Services.Analytics.getTotalCartsOpenReport({
						fromDate: hasDates ? new Date(fromDate) : undefined,
						toDate: hasDates ? new Date(toDate) : undefined,
						employeecode,
						customerCRMID,
					});

					res.status(200).json({
						ok: true,
						message: "Total carts open report fetched successfully",
						data: report,
					});
				} catch (error) {
					logger.error("Error fetching total carts open report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Total Wishlists Created Report
		 * This route fetches the report of created wishlists with details
		 * Can be filtered by date range, employee code, or customer ID
		 */
		"GET /totalWishlistsCreated": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate, employeecode, customerCRMID } = req.query;

					// Check if any filter is provided at all
					if (!fromDate && !toDate && !employeecode && !customerCRMID) {
						return res.status(400).json({
							ok: false,
							message:
								"At least one filter criteria is required: a date range, employeecode, or customerCRMID",
						});
					}

					// If either of the date parameters is provided, ensure both are present
					if ((fromDate || toDate) && (!fromDate || !toDate)) {
						return res.status(400).json({
							ok: false,
							message: "Both fromDate and toDate must be provided when using a date filter",
						});
					}

					// Determine which criteria are set
					const hasDates = fromDate && toDate;
					const hasEmployee = Boolean(employeecode);
					const hasCRM = Boolean(customerCRMID);

					// Ensure that only one criteria is provided:
					// either dates only, employeecode only, or customerCRMID only.
					const criteriaCount = [hasDates, hasEmployee, hasCRM].filter(Boolean).length;

					if (criteriaCount !== 1) {
						return res.status(400).json({
							ok: false,
							message:
								"Only one filter criteria must be provided: either a date range (both fromDate and toDate), employeecode, or customerCRMID",
						});
					}

					const report = await Services.Analytics.getTotalWishlistsCreatedReport({
						fromDate: hasDates ? new Date(fromDate) : undefined,
						toDate: hasDates ? new Date(toDate) : undefined,
						employeecode,
						customerCRMID,
					});

					res.status(200).json({
						ok: true,
						message: "Total wishlists created report fetched successfully",
						data: report,
					});
				} catch (error) {
					logger.error("Error fetching total wishlists created report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate Wishlists Report Excel
		 * This route generates an Excel file containing wishlist data based on filters
		 * Can be filtered by date range, employee code, or customer ID
		 */
		"GET /totalWishlistsCreatedExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate, employeecode, customerCRMID } = req.query;

					// Check if any filter is provided at all
					if (!fromDate && !toDate && !employeecode && !customerCRMID) {
						return res.status(400).json({
							ok: false,
							message:
								"At least one filter criteria is required: a date range, employeecode, or customerCRMID",
						});
					}

					// If either of the date parameters is provided, ensure both are present
					if ((fromDate || toDate) && (!fromDate || !toDate)) {
						return res.status(400).json({
							ok: false,
							message: "Both fromDate and toDate must be provided when using a date filter",
						});
					}

					// Determine which criteria are set
					const hasDates = fromDate && toDate;
					const hasEmployee = Boolean(employeecode);
					const hasCRM = Boolean(customerCRMID);

					// Ensure that only one criteria is provided:
					// either dates only, employeecode only, or customerCRMID only.
					const criteriaCount = [hasDates, hasEmployee, hasCRM].filter(Boolean).length;

					if (criteriaCount !== 1) {
						return res.status(400).json({
							ok: false,
							message:
								"Only one filter criteria must be provided: either a date range (both fromDate and toDate), employeecode, or customerCRMID",
						});
					}

					// Get the Excel file path
					const excelPath = await Services.Analytics.generateTotalWishlistsCreatedExcel({
						fromDate: hasDates ? new Date(fromDate) : undefined,
						toDate: hasDates ? new Date(toDate) : undefined,
						employeecode,
						customerCRMID,
					});

					// Check if the Excel file was generated successfully
					if (!excelPath || !fs.existsSync(excelPath)) {
						return res.status(500).json({
							ok: false,
							message: "Failed to generate Excel file for the wishlists report",
						});
					}

					// Extract the file name from the generated file path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for uploading the file to S3
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "wishlistsReport",
						fileName: fileName,
					});

					// Upload the generated Excel file to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (!uploadRes.ok) {
						// Remove the locally stored Excel file
						if (fs.existsSync(excelPath)) {
							fs.unlinkSync(excelPath);
						}

						return res.status(500).json({
							ok: false,
							message: "Failed to upload Excel file to storage",
						});
					}

					// Get database connection
					const db = Services.MongoHelper.getDatabaseConnection();

					// Ensure TTL index exists for automatic deletion of records after 7 days
					await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

					// Insert the file URL into the database with an expiration time of 7 days
					await db.collection("analyticsExcels").insertOne({
						fileUrl: publicUrl,
						createdAt: new Date(),
						expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Auto-delete after 7 days
						reportType: "wishlistsReport",
						filter: {
							fromDate: hasDates ? fromDate : undefined,
							toDate: hasDates ? toDate : undefined,
							employeecode,
							customerCRMID,
						},
					});

					// Remove the locally stored Excel file to free up space
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}

					res.status(200).json({
						ok: true,
						message: "Wishlists report Excel generated successfully",
						data: {
							fileUrl: publicUrl,
							expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
						},
					});
				} catch (error) {
					logger.error("Error generating wishlists report Excel", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Average SKUs per Quote
		 * This route fetches statistics about the number of SKUs in quotes
		 */
		"GET /quoteSkuStats": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate } = req.query;

					// Validate date parameters
					if (!fromDate || !toDate) {
						return res.status(400).json({
							ok: false,
							message: "Both fromDate and toDate are required",
						});
					}

					// Get quote SKU stats
					const stats = await Services.Analytics.getQuoteSkuStats({
						fromDate: new Date(fromDate),
						toDate: new Date(toDate),
					});

					res.status(200).json({
						ok: true,
						message: "Quote SKU stats fetched successfully",
						data: stats,
					});
				} catch (error) {
					logger.error("Error fetching quote SKU stats", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate Quote SKU Stats Excel
		 * This route generates an Excel file with quote SKU statistics
		 */
		"GET /GenerateQuoteSkuStatsExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fromDate, toDate } = req.query;

					// Validate date parameters
					if (!fromDate || !toDate) {
						return res.status(400).json({
							ok: false,
							message: "Both fromDate and toDate are required",
						});
					}

					const db = Services.MongoHelper.getDatabaseConnection();

					// Format filter parameters
					const filterParams = { fromDate, toDate };
					const reportType = "quoteSkuStats";

					// Check if a recent report exists
					const twentyMinsAgo = new Date(Date.now() - 20 * 60 * 1000);
					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType,
						createdAt: { $gte: twentyMinsAgo },
						filterParams,
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent quote SKU stats report is already available",
							data: recentReport,
						});
					}

					// Immediately respond to avoid blocking
					res.status(200).json({
						ok: true,
						message: "Your quote SKU stats report is being generated and will be available shortly.",
					});

					// Generate Excel file
					const excelPath = await Services.Analytics.generateQuoteSkuStatsExcel({
						fromDate: new Date(fromDate),
						toDate: new Date(toDate),
					});

					// Extract filename from path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for S3 upload
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "quoteSkuStats",
						fileName,
					});

					// Upload to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Store file information
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
							reportType,
							filterParams,
						});
					}

					// Cleanup local file
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error generating quote SKU stats excel", error);
					// Don't try to send a response here if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get Quote Report Excel
		 * This route fetches the quote analytics report Excel files based on type
		 */
		"GET /GetQuoteReportExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { reportType } = req.query;

					if (!reportType || !["quoteCreationTimeStats", "quoteSkuStats"].includes(reportType)) {
						return res.status(400).json({
							ok: false,
							message: "Valid reportType is required: quoteCreationTimeStats or quoteSkuStats",
						});
					}

					const db = Services.MongoHelper.getDatabaseConnection();

					// Fetch the latest Excel file of the specified type
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({
							ok: false,
							message: `No ${reportType} report found`,
						});
					}

					res.status(200).json({
						ok: true,
						message: `${reportType} report fetched successfully`,
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching quote report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Generate SKUs Created From Zoho Excel
		 * Generates an Excel report with SKUs created from Zoho data
		 */
		"GET /GenerateSkusCreatedFromZohoExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					const reportType = "skusCreatedFromZoho";

					// Check if a recent report exists
					const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);
					const recentReport = await db.collection("analyticsExcels").findOne({
						reportType,
						createdAt: { $gte: sixHoursAgo },
					});

					if (recentReport) {
						return res.status(200).json({
							ok: true,
							message: "A recent SKUs from Zoho report is already available",
							data: recentReport,
						});
					}

					// Immediately respond to avoid blocking
					res.status(200).json({
						ok: true,
						message: "Your SKUs from Zoho report is being generated and will be available in 5-10 mins.",
					});

					// Generate Excel file - without date filtering
					const excelPath = await Services.Analytics.generateSkusCreatedFromZohoExcel({});

					// Extract filename from path
					const fileName = excelPath.split("\\").pop();

					// Get signed URL for uploading the file to S3
					const { signedUrl, publicUrl } = await Services.AWSHelpers.getSignedUrlFromS3({
						folderName: "skusFromZoho",
						fileName: fileName,
					});

					// Upload the generated Excel file to S3
					const uploadRes = await Services.AWSHelpers.uploadFileToS3({ signedUrl, file: excelPath });

					if (uploadRes.ok) {
						// Ensure TTL index exists for automatic deletion of records after 30 days
						await db.collection("analyticsExcels").createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

						// Insert the file URL into the database with an expiration time of 30 days
						await db.collection("analyticsExcels").insertOne({
							fileUrl: publicUrl,
							createdAt: new Date(),
							expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Auto-delete after 30 days
							reportType,
						});
					}

					// Remove the locally stored Excel file to free up space
					if (fs.existsSync(excelPath)) {
						fs.unlinkSync(excelPath);
					}
				} catch (error) {
					logger.error("Error generating SKUs from Zoho excel", error);
					// Don't try to send a response if one has already been sent
					if (!res.headersSent) {
						res.status(500).json({ ok: false, message: error.message });
					}
				}
			},
		},

		/**
		 * Get SKUs Created From Zoho Excel
		 * Fetches the latest SKUs created from Zoho Excel report
		 */
		"GET /GetSkusCreatedFromZohoExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					// Fetch the latest SKUs from Zoho Excel file URL from the database
					const excel = await db
						.collection("analyticsExcels")
						.find({ reportType: "skusCreatedFromZoho" })
						.sort({ createdAt: -1 })
						.toArray();

					if (!excel.length > 0) {
						return res.status(404).json({ ok: false, message: "No SKUs from Zoho report found" });
					}

					// Send the file URL to the client
					res.status(200).json({
						ok: true,
						message: "SKUs from Zoho report fetched successfully",
						data: excel,
					});
				} catch (error) {
					logger.error("Error fetching SKUs from Zoho report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Last Orders Sent To Zoho
		 * Fetches the latest orders sent to Zoho report
		 */
		"GET /GetLastOrdersSentToZoho": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Fetch the latest order sent to Zoho
					const order = await Services.Analytics.getLastOrdersSentToZoho();

					if (!order) {
						return res.status(404).json({ ok: false, message: "No orders sent to Zoho found" });
					}

					// Send the order data to the client
					res.status(200).json({
						ok: true,
						message: "Order sent to Zoho fetched successfully",
						data: order,
					});
				} catch (error) {
					logger.error("Error fetching orders sent to Zoho report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Last SKU Details From Zoho Excel
		 * Fetches the latest SKU details updated from Zoho Excel report
		 */
		"GET /GetLastSkuUpdatedFromZoho": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const data = await Services.Analytics.getLastSkuDetailsFromZoho();

					// Send the data to the client
					res.status(200).json({
						ok: true,
						message: "SKU details from Zoho report fetched successfully",
						data: data,
					});
				} catch (error) {
					logger.error("Error fetching SKU details from Zoho report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Last Updates With SF Excel
		 * Fetches the latest updates done with Salesforce Excel report
		 * ❌ since we delete the hold requests after 48 hours, so we will have to change the logic for it in order to get this report
		 */
		"GET /GetLastUpdatesWithSFExcel": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const data = await Services.Analytics.getLastUpdatesWithSF();

					// Send the data to the client

					res.status(200).json({
						ok: true,
						message: "Updates with SF report fetched successfully",
						data: data,
					});
				} catch (error) {
					logger.error("Error fetching updates with SF report", error);
					res.status(500).json({ ok: false, message: error.message });
				}
			},
		},
	};
};
