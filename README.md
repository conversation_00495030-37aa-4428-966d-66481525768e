# Grogu.js (light weight express boilerplate)

## Usage Documentation

Click [here](./docs/index.md) for usage documentation

## How to run the app

The entry point of the project is `app.js`

### For Running in Dev

```sh
npm run dev # This will run the project in development environment
```

For running the above command `.env.dev` file should exist inside the folder root.

### For Running in Prod

```sh
npm run start # This will run the project in prod environment
```

For running the above command `.env` file should exist inside the folder root.

// Node version of the project is v20.14.0