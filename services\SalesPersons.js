/**
 * SalesPersons Service
 * SalesPersons service functions defined here
 */

const { logger } = require("../utils");
const jwt = require("jsonwebtoken");
const { ADLogin } = require("../helpers/ADLogin");

module.exports = async function ({ Services, config }) {
	return {
		login: async ({ email, password }) => {
			try {
				const empcode = email.split("@")[0].toUpperCase();
				// if (
				// 	config.env === "staging" &&
				// 	!["*********", "*********", "*********", "*********"].includes(empcode)
				// ) {
				// 	return {
				// 		ok: false,
				// 		message:
				// 			"You do not have permission to access the test environment. Please log in at anthology.foyr.com.",
				// 	};
				// }

				const db = await Services.MongoHelper.getDatabaseConnection();

				// AD Login
				const { ok, data: adUser } = await ADLogin({ email, password });

				if (!ok) {
					return { ok: false, message: "Invalid email or password" };
				}

				// trim the email till @ if employee code is not present
				if (!adUser.employeecode) {
					adUser.employeecode = email.split("@")[0].toUpperCase();
				}

				// Update or insert the sales person
				const salesPerson = await db.collection("salesPersons").findOneAndUpdate(
					{ email: adUser.email },
					{
						$set: {
							...adUser,
							updatedAt: new Date(),
						},
					},
					{ upsert: true, returnOriginal: false }
				);

				// Check if there's an existing valid token
				const existingToken = await db.collection("oidcs").findOne({
					salesPersonId: salesPerson._id,
					createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Token created within the last 24 hours
				});

				let token;
				if (existingToken) {
					token = existingToken.token;
				} else {
					// Generate a new token for the user
					token = jwt.sign({ userId: salesPerson._id.toString() }, config.ap.jwtSecret, {
						expiresIn: "1d",
					});

					// Save the new token in the oidcs collection
					await db.collection("oidcs").insertOne({
						token,
						salesPersonId: salesPerson._id,
						createdAt: new Date(),
						updatedAt: new Date(),
					});
				}

				// Prepare user details to return
				const userDetails = {
					_id: salesPerson._id,
					email: salesPerson.email,
					username: salesPerson.username,
					employeecode: salesPerson.employeecode,
				};

				return { ok: true, token, userDetails };
			} catch (error) {
				logger.error(error);
				return {
					ok: false,
					message: "We are facing an issue with our database, please try again after a while.",
				};
			}
		},

		// Update mobile number for the sales person
		updateMobile: async (query) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const { _id, mobile, name } = query;
				if (!mobile) {
					return { ok: false, message: "Mobile number is required" };
				}
				const updatedUser = await db.collection("salesPersons").findOneAndUpdate(
					{ _id }, // Filter to find the user
					{ $set: { mobile, updatedAt: new Date(), name } }, // Update fields
					{ returnDocument: "after" } // Return updated document after modification
				);
				return { ok: true, user: updatedUser };
			} catch (e) {
				logger.error(e);
				return { ok: false, message: "Internal server error" };
			}
		},

		logout: async ({ token }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Remove the token from the oidcs collection
				const result = await db.collection("oidcs").deleteOne({ token });

				if (result.deletedCount === 0) {
					return { ok: false, message: "Invalid token or already logged out" };
				}

				return { ok: true, message: "Successfully logged out" };
			} catch (error) {
				logger.error(error);
				return { ok: false, message: "Internal server error" };
			}
		},
	};
};
