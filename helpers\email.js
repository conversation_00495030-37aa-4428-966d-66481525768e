module.exports = async function (user, quoteId, config) {
	return `
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Responsive Meta Tag -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        /* General Reset */
        body, table, td, div, p {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Arial, sans-serif;
            color: #333333;
        }

        body {
            background-color: #ffffff;
        }

        /* Container */
        .container {
            max-width: 600px;
            margin: 20px auto;
            background: #ffffff;
            padding: 30px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 20px;
        }

        .header .logo {
            margin-right: 20px;          /* space between logo and heading */
        }

        .header .logo img {
            max-width: 100px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
            color: #333333;
        }

        /* Content */
        .content {
            margin-top: 25px;
        }

        .content p {
            line-height: 1.6;
            color: #555555;
            margin: 12px 0;
        }

        /* Attachment Info Box */
        .attachment-info {
            background-color: #ffffff;
            border-left: 3px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
        }

        /* Button */
        .button {
            display: inline-block;
            margin: 20px 0;
            padding: 12px 24px;
            font-size: 15px;
            color: #ffffff !important;
            background-color: #0066cc;
            text-decoration: none;
            border-radius: 3px;
            font-weight: 500;
        }

        /* Signature */
        .signature {
            margin-top: 30px;
        }

        /* Footer */
        .footer {
            text-align: center;
            font-size: 12px;
            color: #888888;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        /* Responsive Styles */
        @media only screen and (max-width: 600px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            .header .logo {
                margin-right: 0;        /* remove margin on smaller screens */
                margin-bottom: 10px;    /* add some bottom space instead */
            }
            .header h1 {
                font-size: 20px;
            }
            .container {
                width: 95% !important;
                margin: 10px auto !important;
                padding: 20px !important;
                box-shadow: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="https://foyr-manage-tasks.s3.ap-south-1.amazonaws.com/apbungalow/logo.png" alt="Nilaya Anthology Logo">
            </div>
            <h1>Quotation Details</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear ${user.name},</p>
            <p>Thank you for your interest in Nilaya Anthology. We are pleased to provide you with the quotation you requested for our products and services.</p>

            <div class="attachment-info">
                <p>🔗 Your quotation document is attached to this email as a PDF file for your reference and records.</p>
            </div>

            <p>For your convenience, you may also view this quotation online through our customer portal by clicking the button below:</p>

            <div style="text-align: center;">
                <a href="${config.FRONTEND_URL}/#/quote/${quoteId}" class="button">View Quotation Online</a>
            </div>

            <p>Should you have any questions regarding this quotation or require any clarification, please do not hesitate to contact our customer service team.</p>

            <div class="signature">
                <p>Sincerely,</p>
                <p><strong>Customer Support Team</strong><br>Nilaya Anthology</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2025 Nilaya Anthology Ltd. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;
};
