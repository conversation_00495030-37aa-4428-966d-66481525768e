const jwt = require("jsonwebtoken");
const config = require("../config/conf");
const { ObjectId } = require("mongodb");
const { logger } = require("../utils");

module.exports = async function (req, res, next, { Services /*, config */ }) {
	// Get the token from the Authorization header
	const authHeader = req.headers.authorization;
	if (!authHeader || !authHeader.startsWith("Bearer ")) {
		return res.status(401).json({ ok: false, message: "No token provided" });
	}

	const token = authHeader.split(" ")[1];

	try {
		// Verify the token
		const decoded = jwt.verify(token, config.ap.jwtSecret);

		// Get the database connection
		const db = await Services.MongoHelper.getDatabaseConnection();

		// Check if the token exists in the oidcs collection
		const oidc = await db.collection("oidcs").findOne({
			token: token,
			salesPersonId: ObjectId.createFromHexString(decoded.userId),
		});

		if (!oidc) {
			return res.status(401).json({ ok: false, message: "Invalid token" });
		}

		// Check if the token has expired (24 hours)
		const tokenAge = Date.now() - oidc.createdAt.getTime();
		if (tokenAge > 24 * 60 * 60 * 1000) {
			return res.status(401).json({ ok: false, message: "Token expired" });
		}

		// Fetch the user from the database
		const user = await db.collection("salesPersons").findOne({ _id: ObjectId.createFromHexString(decoded.userId) });

		if (!user) {
			return res.status(401).json({ ok: false, message: "User not found" });
		}

		// Attach the user to the request object
		req.user = user;
		req.token = token;

		next();
	} catch (error) {
		if (error instanceof jwt.JsonWebTokenError) {
			return res.status(401).json({ ok: false, message: "Invalid token" });
		}
		logger.error("Error in isLoggedIn middleware:", error);
		return res.status(500).json({ ok: false, message: "Internal server error" });
	}
};
