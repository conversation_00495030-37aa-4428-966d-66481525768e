/**
 * Cleanup Duplicate Products Script
 * This script removes duplicate products, keeping the most recent one
 */

require('dotenv').config();

// Use the same configuration as your app
const config = require('./config/conf.js');
config.rootDir = __dirname;

async function cleanupDuplicates() {
    try {
        // Use your existing mongo connection logic
        const mongoConnector = require('./helpers/mongoConnector');
        const db = await mongoConnector({ mongo: config.mongo });

        console.log('🔍 Finding duplicate products...');

        // Find all duplicate products grouped by 'code' field
        const duplicates = await db.collection('products').aggregate([
            {
                $group: {
                    _id: "$code",
                    count: { $sum: 1 },
                    docs: { $push: { id: "$_id", createdAt: "$createdAt", updatedAt: "$updatedAt", createdBy: "$createdBy" } }
                }
            },
            {
                $match: {
                    count: { $gt: 1 }
                }
            }
        ]).toArray();

        console.log(`📊 Found ${duplicates.length} SKUs with duplicates`);

        let totalRemoved = 0;

        for (const duplicate of duplicates) {
            const { _id: code, docs } = duplicate;
            
            console.log(`\n🔧 Processing SKU: ${code} (${docs.length} duplicates)`);

            // Sort by: prefer PIM > Zoho, then most recent updatedAt, then most recent createdAt
            docs.sort((a, b) => {
                // Prefer PIM over Zoho (PIM has richer data)
                if (a.createdBy === 'PIM' && b.createdBy !== 'PIM') return -1;
                if (b.createdBy === 'PIM' && a.createdBy !== 'PIM') return 1;
                
                // Then by most recent updatedAt
                if (a.updatedAt && b.updatedAt) {
                    return new Date(b.updatedAt) - new Date(a.updatedAt);
                }
                
                // Then by most recent createdAt
                if (a.createdAt && b.createdAt) {
                    return new Date(b.createdAt) - new Date(a.createdAt);
                }
                
                return 0;
            });

            // Keep the first one (best candidate), remove the rest
            const toKeep = docs[0];
            const toRemove = docs.slice(1);

            console.log(`   ✅ Keeping: ${toKeep.id} (${toKeep.createdBy}, ${toKeep.updatedAt || toKeep.createdAt})`);

            for (const doc of toRemove) {
                console.log(`   ❌ Removing: ${doc.id} (${doc.createdBy}, ${doc.updatedAt || doc.createdAt})`);
                await db.collection('products').deleteOne({ _id: doc.id });
                totalRemoved++;
            }
        }

        console.log(`\n🎉 Cleanup completed!`);
        console.log(`📈 Total duplicate products removed: ${totalRemoved}`);
        console.log(`🏷️  SKUs with duplicates processed: ${duplicates.length}`);

        // Verify no duplicates remain
        const remainingDuplicates = await db.collection('products').aggregate([
            {
                $group: {
                    _id: "$code",
                    count: { $sum: 1 }
                }
            },
            {
                $match: {
                    count: { $gt: 1 }
                }
            }
        ]).toArray();

        if (remainingDuplicates.length === 0) {
            console.log('✅ Verification: No duplicate SKUs remain in database');
        } else {
            console.log(`⚠️  Warning: ${remainingDuplicates.length} SKUs still have duplicates`);
        }

        await db.close();

    } catch (error) {
        console.error('❌ Error during cleanup:', error);
        process.exit(1);
    }
}

// Run the cleanup
cleanupDuplicates()
    .then(() => {
        console.log('\n🎯 Next steps:');
        console.log('1. Uncomment the indexes in your model files');
        console.log('2. Restart your application');
        console.log('3. The unique indexes will be created automatically');
        console.log('4. Future duplicates will be prevented by the compound filters');
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    });