const { ObjectId } = require("mongodb");

module.exports = {
    collections:[
        "customers",
        "sessions",
        "products",
        "holdRequests",
        "wishlists",
        "addresses",
        "carts",
        "quotes",
        "salesOrders",
        "analyticsExcels",
        "cronSchedules",
        "customerFeedbacks",
        "loginLogs",
        "miscellaneous",
        "notes",
        "oidcs",
        "productEvents",
        "quotesShareHistory",
        "salesPersons",
        "stateDescriptions",
        "statePincode",
    ],
    // Database indexes to prevent duplicates and improve performance
    indexes: {
        products: [
            [{ code: 1 }, { unique: true }], // Unique constraint on SKU code
            [{ item_id: 1 }, { unique: true, sparse: true }] // Unique constraint on item_id (sparse allows null/missing values)
        ]
    },
    // schema for information only
    schema:{
        customers:{
            _id: "ObjectId (primary key)",
			name: "string",
			email: "string",
			address: "string",
			mobile: "string",
			createdAt: "date",
			updatedAt: "date",
            isArchitect: "Boolean", // by default false
        }
    }
}