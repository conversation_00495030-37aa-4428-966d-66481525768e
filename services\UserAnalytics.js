const { logger } = require("../utils");
const { ObjectId } = require("mongodb");

/**
 * UserAnalytics Service
 * Provides analytics data at the user level
 */
module.exports = async function ({ Services, config }) {
    // Helper function to get analytics for a single user
    const getUserAnalyticsData = async (user, options) => {
        const { startDate, endDate } = options;
        const db = await Services.MongoHelper.getDatabaseConnection();
        const employeecode = user.employeecode;

        const userData = {
            name: user.name || user.username || "Unknown",
            pcode: employeecode,
            loginCount: 0,
            customersCreated: 0,
            quotesCreated: 0,
            totalQuoteValue: 0,
            salesOrdersCreated: 0,
            totalSalesOrderValue: 0
        };

        // Build date filter for queries
        const dateFilter = {};
        if (startDate) {
            dateFilter.$gte = startDate;
        }
        if (endDate) {
            dateFilter.$lte = endDate;
        }

        // Count login events
        const loginQuery = {
            employeecode,
            event: "login"
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            loginQuery.timestamp = dateFilter;
        }

        const loginEvents = await db.collection("loginLogs").countDocuments(loginQuery);
        userData.loginCount = loginEvents;

        // Count customers created by this user
        const customersQuery = {
            salesRepPID: employeecode
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            customersQuery.createdAt = dateFilter;
        }

        const customersCreated = await db.collection("customers").countDocuments(customersQuery);
        userData.customersCreated = customersCreated;

        // Get quotes created by this user
        const quotesQuery = {
            salesRepPID: employeecode
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            quotesQuery.createdAt = dateFilter;
        }

        const quotes = await db.collection("quotes").find(quotesQuery).toArray();

        userData.quotesCreated = quotes.length;

        // Calculate total quote value
        let totalQuoteValue = 0;
        quotes.forEach(quote => {
            // Use grand_total_WITH_DISCOUNT if available, otherwise use grand_total_WITHOUT_DISCOUNT
            const quoteValue = parseFloat(quote.grand_total_WITH_DISCOUNT) ||
                              parseFloat(quote.grand_total_WITHOUT_DISCOUNT) || 0;
            totalQuoteValue += quoteValue;
        });
        userData.totalQuoteValue = totalQuoteValue;

        // Get all quotes with their Salesforce IDs
        const quotesWithSalesCRMIDQuery = {
            salesRepPID: employeecode,
            quoteSalesCRMID: { $exists: true, $ne: null }
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            quotesWithSalesCRMIDQuery.createdAt = dateFilter;
        }

        const allQuotesWithSalesCRMID = await db.collection("quotes")
            .find(quotesWithSalesCRMIDQuery)
            .toArray();

        // Extract the zoho sales order IDs
        const userSalesOrderIDs = allQuotesWithSalesCRMID.map(q => q.salesOrderId);

        // Get sales orders that reference this user's quotes
        const salesOrdersQuery = {
            salesorder_id: { $in: userSalesOrderIDs }
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            salesOrdersQuery.created_time = dateFilter;
        }
        console.log(salesOrdersQuery);

        const userSalesOrders = await db.collection("salesOrders").find(salesOrdersQuery).toArray();

        userData.salesOrdersCreated = userSalesOrders.length;

        // Calculate total sales order value
        let totalSalesOrderValue = 0;
        userSalesOrders.forEach(so => {
            // Try different possible field names for the total value
            const soValue = parseFloat(so.total) ||
                           parseFloat(so.total_amount) ||
                           parseFloat(so.sub_total) ||
                           parseFloat(so.balance) || 0;
            totalSalesOrderValue += soValue;
        });
        userData.totalSalesOrderValue = totalSalesOrderValue;

        return userData;
    };

    return {
        /**
         * Get user-level analytics data
         * @param {Object} options - Query options
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @param {string} [options.employeecode] - Employee code to filter by specific user
         * @returns {Object} User-level analytics data
         */
        getUserAnalytics: async (options = {}) => {
            try {
                const db = await Services.MongoHelper.getDatabaseConnection();

                // If employeecode is provided, get analytics for just that user
                if (options.employeecode) {
                    const user = await db.collection("salesPersons").findOne({ employeecode: options.employeecode });
                    if (!user) {
                        return { ok: false, message: "User not found" };
                    }

                    const userData = await getUserAnalyticsData(user, options);
                    return { ok: true, data: [userData] };
                }

                // Otherwise, get analytics for all users
                const salesPersons = await db.collection("salesPersons").find({}).toArray();

                // Initialize results array
                const results = [];

                // Process each sales person
                for (const user of salesPersons) {
                    if (!user.employeecode) continue; // Skip users without employee code

                    const userData = await getUserAnalyticsData(user, options);
                    results.push(userData);
                }

                return { ok: true, data: results };
            } catch (error) {
                logger.error("Error getting user analytics:", error);
                return { ok: false, message: error.message };
            }
        },

        /**
         * Get analytics for a specific user
         * @param {Object} options - Query options
         * @param {string} options.employeecode - Employee code of the user
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @returns {Object} User analytics data
         */
        getUserAnalyticsByEmployeeCode: async (options) => {
            if (!options.employeecode) {
                return { ok: false, message: "Employee code is required" };
            }

            return await Services.UserAnalytics.getUserAnalytics(options);
        },

        /**
         * Get date-wise analytics data
         * @param {Object} options - Query options
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @param {string} [options.employeecode] - Employee code to filter by specific user
         * @returns {Object} Date-wise analytics data
         */
        getDateWiseAnalytics: async (options = {}) => {
            try {
                const { startDate, endDate, employeecode } = options;
                const db = await Services.MongoHelper.getDatabaseConnection();

                // Build date filter
                const dateFilter = {};
                if (startDate) {
                    dateFilter.$gte = startDate;
                }
                if (endDate) {
                    dateFilter.$lte = endDate;
                }

                // Build user filter
                const userFilter = {};
                if (employeecode) {
                    userFilter.salesRepPID = employeecode;
                }

                // Aggregate customers by date
                const customersAggregation = [
                    {
                        $match: {
                            ...userFilter,
                            ...(Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {})
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                            },
                            customersCount: { $sum: 1 }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const customersData = await db.collection("customers").aggregate(customersAggregation).toArray();

                // Aggregate quotes by date
                const quotesAggregation = [
                    {
                        $match: {
                            ...userFilter,
                            ...(Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {})
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                            },
                            quotesCount: { $sum: 1 },
                            totalQuoteValue: {
                                $sum: {
                                    $cond: [
                                        { $ne: ["$grand_total_WITH_DISCOUNT", null] },
                                        { $toDouble: "$grand_total_WITH_DISCOUNT" },
                                        { $toDouble: "$grand_total_WITHOUT_DISCOUNT" }
                                    ]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const quotesData = await db.collection("quotes").aggregate(quotesAggregation).toArray();

                // Get sales orders data
                // First, get all quotes with their Salesforce IDs for the user
                const quotesQuery = {
                    quoteSalesCRMID: { $exists: true, $ne: null },
                    ...userFilter,
                    ...(Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {})
                };

                const userQuotes = await db.collection("quotes").find(quotesQuery).toArray();
                const userQuoteSalesCRMIDs = userQuotes.map(q => q.quoteSalesCRMID);

                // Aggregate sales orders by date
                const salesOrdersAggregation = [
                    {
                        $match: {
                            quoteCRMIDS: { $in: userQuoteSalesCRMIDs },
                            ...(Object.keys(dateFilter).length > 0 ? {
                                created_time: {
                                    $gte: startDate ? startDate.toISOString() : new Date("1970-01-01").toISOString(),
                                    $lte: endDate ? endDate.toISOString() : new Date().toISOString()
                                }
                            } : {})
                        }
                    },
                    {
                        $addFields: {
                            createdDate: {
                                $dateFromString: {
                                    dateString: "$created_time",
                                    onError: new Date()
                                }
                            }
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdDate" }
                            },
                            salesOrdersCount: { $sum: 1 },
                            totalSalesOrderValue: {
                                $sum: {
                                    $cond: [
                                        { $ne: ["$total", null] },
                                        { $toDouble: "$total" },
                                        {
                                            $cond: [
                                                { $ne: ["$total_amount", null] },
                                                { $toDouble: "$total_amount" },
                                                {
                                                    $cond: [
                                                        { $ne: ["$sub_total", null] },
                                                        { $toDouble: "$sub_total" },
                                                        0
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const salesOrdersData = await db.collection("salesOrders").aggregate(salesOrdersAggregation).toArray();

                // Combine the data by date
                const dateMap = new Map();

                // Add customers data
                customersData.forEach(item => {
                    dateMap.set(item._id, {
                        date: item._id,
                        customersCount: item.customersCount || 0,
                        quotesCount: 0,
                        totalQuoteValue: 0,
                        salesOrdersCount: 0,
                        totalSalesOrderValue: 0
                    });
                });

                // Add quotes data
                quotesData.forEach(item => {
                    if (dateMap.has(item._id)) {
                        const existing = dateMap.get(item._id);
                        existing.quotesCount = item.quotesCount || 0;
                        existing.totalQuoteValue = item.totalQuoteValue || 0;
                    } else {
                        dateMap.set(item._id, {
                            date: item._id,
                            customersCount: 0,
                            quotesCount: item.quotesCount || 0,
                            totalQuoteValue: item.totalQuoteValue || 0,
                            salesOrdersCount: 0,
                            totalSalesOrderValue: 0
                        });
                    }
                });

                // Add sales orders data
                salesOrdersData.forEach(item => {
                    if (dateMap.has(item._id)) {
                        const existing = dateMap.get(item._id);
                        existing.salesOrdersCount = item.salesOrdersCount || 0;
                        existing.totalSalesOrderValue = item.totalSalesOrderValue || 0;
                    } else {
                        dateMap.set(item._id, {
                            date: item._id,
                            customersCount: 0,
                            quotesCount: 0,
                            totalQuoteValue: 0,
                            salesOrdersCount: item.salesOrdersCount || 0,
                            totalSalesOrderValue: item.totalSalesOrderValue || 0
                        });
                    }
                });

                // Convert map to array and sort by date
                const results = Array.from(dateMap.values()).sort((a, b) => a.date.localeCompare(b.date));

                return { ok: true, data: results };
            } catch (error) {
                logger.error("Error getting date-wise analytics:", error);
                return { ok: false, message: error.message };
            }
        },

        /**
         * Get date-wise analytics data for all users combined
         * @param {Object} options - Query options
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @returns {Object} Date-wise analytics data for all users
         */
        getDateWiseAnalyticsAllUsers: async (options = {}) => {
            try {
                const { startDate, endDate } = options;
                const db = await Services.MongoHelper.getDatabaseConnection();

                // Build date filter
                const dateFilter = {};
                if (startDate) {
                    dateFilter.$gte = startDate;
                }
                if (endDate) {
                    dateFilter.$lte = endDate;
                }

                // Aggregate customers by date (all users)
                const customersAggregation = [
                    {
                        $match: {
                            ...(Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {})
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                            },
                            customersCount: { $sum: 1 }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const customersData = await db.collection("customers").aggregate(customersAggregation).toArray();

                // Aggregate quotes by date (all users)
                const quotesAggregation = [
                    {
                        $match: {
                            ...(Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {})
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
                            },
                            quotesCount: { $sum: 1 },
                            totalQuoteValue: {
                                $sum: {
                                    $cond: [
                                        { $ne: ["$grand_total_WITH_DISCOUNT", null] },
                                        { $toDouble: "$grand_total_WITH_DISCOUNT" },
                                        { $toDouble: "$grand_total_WITHOUT_DISCOUNT" }
                                    ]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const quotesData = await db.collection("quotes").aggregate(quotesAggregation).toArray();

                // Aggregate sales orders by date (all users)
                const salesOrdersAggregation = [
                    {
                        $match: {
                            ...(Object.keys(dateFilter).length > 0 ? {
                                created_time: {
                                    $gte: startDate ? startDate.toISOString() : new Date("1970-01-01").toISOString(),
                                    $lte: endDate ? endDate.toISOString() : new Date().toISOString()
                                }
                            } : {})
                        }
                    },
                    {
                        $addFields: {
                            createdDate: {
                                $dateFromString: {
                                    dateString: "$created_time",
                                    onError: new Date()
                                }
                            }
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $dateToString: { format: "%Y-%m-%d", date: "$createdDate" }
                            },
                            salesOrdersCount: { $sum: 1 },
                            totalSalesOrderValue: {
                                $sum: {
                                    $cond: [
                                        { $ne: ["$total", null] },
                                        { $toDouble: "$total" },
                                        {
                                            $cond: [
                                                { $ne: ["$total_amount", null] },
                                                { $toDouble: "$total_amount" },
                                                {
                                                    $cond: [
                                                        { $ne: ["$sub_total", null] },
                                                        { $toDouble: "$sub_total" },
                                                        0
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ];

                const salesOrdersData = await db.collection("salesOrders").aggregate(salesOrdersAggregation).toArray();

                // Combine the data by date
                const dateMap = new Map();

                // Add customers data
                customersData.forEach(item => {
                    dateMap.set(item._id, {
                        date: item._id,
                        customersCount: item.customersCount || 0,
                        quotesCount: 0,
                        totalQuoteValue: 0,
                        salesOrdersCount: 0,
                        totalSalesOrderValue: 0
                    });
                });

                // Add quotes data
                quotesData.forEach(item => {
                    if (dateMap.has(item._id)) {
                        const existing = dateMap.get(item._id);
                        existing.quotesCount = item.quotesCount || 0;
                        existing.totalQuoteValue = item.totalQuoteValue || 0;
                    } else {
                        dateMap.set(item._id, {
                            date: item._id,
                            customersCount: 0,
                            quotesCount: item.quotesCount || 0,
                            totalQuoteValue: item.totalQuoteValue || 0,
                            salesOrdersCount: 0,
                            totalSalesOrderValue: 0
                        });
                    }
                });

                // Add sales orders data
                salesOrdersData.forEach(item => {
                    if (dateMap.has(item._id)) {
                        const existing = dateMap.get(item._id);
                        existing.salesOrdersCount = item.salesOrdersCount || 0;
                        existing.totalSalesOrderValue = item.totalSalesOrderValue || 0;
                    } else {
                        dateMap.set(item._id, {
                            date: item._id,
                            customersCount: 0,
                            quotesCount: 0,
                            totalQuoteValue: 0,
                            salesOrdersCount: item.salesOrdersCount || 0,
                            totalSalesOrderValue: item.totalSalesOrderValue || 0
                        });
                    }
                });

                // Convert map to array and sort by date
                const results = Array.from(dateMap.values()).sort((a, b) => a.date.localeCompare(b.date));

                return { ok: true, data: results };
            } catch (error) {
                logger.error("Error getting date-wise analytics for all users:", error);
                return { ok: false, message: error.message };
            }
        }
    };
};
